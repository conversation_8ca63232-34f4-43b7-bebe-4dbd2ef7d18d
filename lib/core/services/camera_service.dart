import 'dart:async';
import 'dart:ui' as ui;
import 'package:camera/camera.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:permission_handler/permission_handler.dart';

/// Camera service for handling camera operations
class CameraService extends ChangeNotifier {
  CameraController? _controller;
  List<CameraDescription> _cameras = [];
  bool _isInitialized = false;
  bool _isDisposed = false;
  String? _error;
  
  // Camera state
  int _selectedCameraIndex = 0;
  bool _isFlashOn = false;
  double _zoomLevel = 1.0;
  double _minZoom = 1.0;
  double _maxZoom = 1.0;
  
  // Stream for camera frames
  StreamController<CameraImage>? _frameStreamController;
  bool _isStreamingFrames = false;

  // Getters
  CameraController? get controller => _controller;
  bool get isInitialized => _isInitialized && _controller?.value.isInitialized == true;
  bool get isDisposed => _isDisposed;
  String? get error => _error;
  bool get isFlashOn => _isFlashOn;
  double get zoomLevel => _zoomLevel;
  double get minZoom => _minZoom;
  double get maxZoom => _maxZoom;
  bool get isRearCamera => _selectedCameraIndex == 0;
  Stream<CameraImage>? get frameStream => _frameStreamController?.stream;

  /// Initialize camera service
  Future<bool> initialize() async {
    try {
      _error = null;
      
      // Request camera permission
      final permissionStatus = await _requestCameraPermission();
      if (!permissionStatus) {
        _error = 'Camera permission denied';
        notifyListeners();
        return false;
      }

      // Get available cameras
      _cameras = await availableCameras();
      if (_cameras.isEmpty) {
        _error = 'No cameras available';
        notifyListeners();
        return false;
      }

      // Initialize with rear camera (index 0)
      await _initializeCamera(0);
      
      return true;
    } catch (e) {
      _error = 'Failed to initialize camera: $e';
      print(_error);
      notifyListeners();
      return false;
    }
  }

  /// Request camera permission
  Future<bool> _requestCameraPermission() async {
    final status = await Permission.camera.request();
    return status == PermissionStatus.granted;
  }

  /// Initialize camera with specific index
  Future<void> _initializeCamera(int cameraIndex) async {
    if (cameraIndex >= _cameras.length) {
      throw ArgumentError('Invalid camera index');
    }

    // Dispose existing controller
    await _controller?.dispose();

    // Create new controller
    _controller = CameraController(
      _cameras[cameraIndex],
      ResolutionPreset.high,
      enableAudio: false,
      imageFormatGroup: ImageFormatGroup.yuv420,
    );

    // Initialize controller
    await _controller!.initialize();
    
    // Get zoom capabilities
    _minZoom = await _controller!.getMinZoomLevel();
    _maxZoom = await _controller!.getMaxZoomLevel();
    _zoomLevel = _minZoom;
    
    _selectedCameraIndex = cameraIndex;
    _isInitialized = true;
    
    notifyListeners();
  }

  /// Switch between front and rear camera
  Future<void> switchCamera() async {
    if (_cameras.length < 2) return;
    
    final newIndex = _selectedCameraIndex == 0 ? 1 : 0;
    await _initializeCamera(newIndex);
  }

  /// Toggle flash
  Future<void> toggleFlash() async {
    if (!isInitialized) return;
    
    try {
      _isFlashOn = !_isFlashOn;
      await _controller!.setFlashMode(_isFlashOn ? FlashMode.torch : FlashMode.off);
      notifyListeners();
    } catch (e) {
      print('Error toggling flash: $e');
    }
  }

  /// Set zoom level
  Future<void> setZoomLevel(double zoom) async {
    if (!isInitialized) return;
    
    try {
      final clampedZoom = zoom.clamp(_minZoom, _maxZoom);
      await _controller!.setZoomLevel(clampedZoom);
      _zoomLevel = clampedZoom;
      notifyListeners();
    } catch (e) {
      print('Error setting zoom: $e');
    }
  }

  /// Start streaming camera frames for ML processing
  void startFrameStream() {
    if (!isInitialized || _isStreamingFrames) return;
    
    _frameStreamController = StreamController<CameraImage>.broadcast();
    _isStreamingFrames = true;
    
    _controller!.startImageStream((CameraImage image) {
      if (!_isDisposed && _frameStreamController != null) {
        _frameStreamController!.add(image);
      }
    });
  }

  /// Stop streaming camera frames
  Future<void> stopFrameStream() async {
    if (!_isStreamingFrames) return;
    
    try {
      await _controller?.stopImageStream();
      await _frameStreamController?.close();
      _frameStreamController = null;
      _isStreamingFrames = false;
    } catch (e) {
      print('Error stopping frame stream: $e');
    }
  }

  /// Capture a photo
  Future<XFile?> capturePhoto() async {
    if (!isInitialized) return null;
    
    try {
      // Temporarily stop frame streaming for capture
      final wasStreaming = _isStreamingFrames;
      if (wasStreaming) {
        await stopFrameStream();
      }
      
      final photo = await _controller!.takePicture();
      
      // Resume frame streaming if it was active
      if (wasStreaming) {
        startFrameStream();
      }
      
      return photo;
    } catch (e) {
      print('Error capturing photo: $e');
      return null;
    }
  }

  /// Convert CameraImage to ui.Image for ML processing
  Future<ui.Image?> convertCameraImageToUIImage(CameraImage cameraImage) async {
    try {
      // Convert YUV420 to RGB
      final rgbBytes = _convertYUV420ToRGB(cameraImage);
      
      // Create ui.Image from RGB bytes
      final completer = Completer<ui.Image>();
      ui.decodeImageFromPixels(
        rgbBytes,
        cameraImage.width,
        cameraImage.height,
        ui.PixelFormat.rgba8888,
        completer.complete,
      );
      
      return await completer.future;
    } catch (e) {
      print('Error converting camera image: $e');
      return null;
    }
  }

  /// Convert YUV420 camera image to RGB bytes
  Uint8List _convertYUV420ToRGB(CameraImage image) {
    final int width = image.width;
    final int height = image.height;
    final int uvRowStride = image.planes[1].bytesPerRow;
    final int uvPixelStride = image.planes[1].bytesPerPixel!;
    
    final Uint8List rgbBytes = Uint8List(width * height * 4); // RGBA
    
    for (int y = 0; y < height; y++) {
      for (int x = 0; x < width; x++) {
        final int yIndex = y * width + x;
        final int uvIndex = uvPixelStride * (x / 2).floor() + uvRowStride * (y / 2).floor();
        
        final int yValue = image.planes[0].bytes[yIndex];
        final int uValue = image.planes[1].bytes[uvIndex];
        final int vValue = image.planes[2].bytes[uvIndex];
        
        // YUV to RGB conversion
        final int r = (yValue + 1.402 * (vValue - 128)).round().clamp(0, 255);
        final int g = (yValue - 0.344136 * (uValue - 128) - 0.714136 * (vValue - 128)).round().clamp(0, 255);
        final int b = (yValue + 1.772 * (uValue - 128)).round().clamp(0, 255);
        
        final int rgbIndex = yIndex * 4;
        rgbBytes[rgbIndex] = r;
        rgbBytes[rgbIndex + 1] = g;
        rgbBytes[rgbIndex + 2] = b;
        rgbBytes[rgbIndex + 3] = 255; // Alpha
      }
    }
    
    return rgbBytes;
  }

  /// Pause camera (for app lifecycle)
  Future<void> pause() async {
    await stopFrameStream();
  }

  /// Resume camera (for app lifecycle)
  Future<void> resume() async {
    if (isInitialized) {
      startFrameStream();
    }
  }

  /// Dispose camera service
  @override
  void dispose() {
    _isDisposed = true;
    stopFrameStream();
    _controller?.dispose();
    super.dispose();
  }
}
