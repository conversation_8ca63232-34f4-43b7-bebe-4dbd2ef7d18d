import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:device_info_plus/device_info_plus.dart';

/// Service for managing performance optimization and device capabilities
class PerformanceService extends ChangeNotifier {
  static final PerformanceService _instance = PerformanceService._internal();
  factory PerformanceService() => _instance;
  PerformanceService._internal();

  // Device capabilities
  bool _isHighPerformanceDevice = false;
  int _recommendedFrameSkip = 2;
  int _recommendedImageSize = 320;
  double _targetFps = 15.0;
  
  // Performance metrics
  double _currentFps = 0.0;
  double _averageInferenceTime = 0.0;
  int _memoryUsageMB = 0;
  
  // Performance tracking
  final List<double> _inferenceTimesMs = [];
  final List<double> _fpsHistory = [];
  Timer? _performanceMonitor;
  
  // Getters
  bool get isHighPerformanceDevice => _isHighPerformanceDevice;
  int get recommendedFrameSkip => _recommendedFrameSkip;
  int get recommendedImageSize => _recommendedImageSize;
  double get targetFps => _targetFps;
  double get currentFps => _currentFps;
  double get averageInferenceTime => _averageInferenceTime;
  int get memoryUsageMB => _memoryUsageMB;

  /// Initialize performance service
  Future<void> initialize() async {
    try {
      await _detectDeviceCapabilities();
      _optimizeSettings();
      _startPerformanceMonitoring();
      print('Performance service initialized');
    } catch (e) {
      print('Error initializing performance service: $e');
    }
  }

  /// Detect device capabilities
  Future<void> _detectDeviceCapabilities() async {
    try {
      final deviceInfo = DeviceInfoPlugin();
      
      if (Platform.isAndroid) {
        final androidInfo = await deviceInfo.androidInfo;
        _analyzeAndroidDevice(androidInfo);
      } else if (Platform.isIOS) {
        final iosInfo = await deviceInfo.iosInfo;
        _analyzeIOSDevice(iosInfo);
      }
    } catch (e) {
      print('Error detecting device capabilities: $e');
      // Default to conservative settings
      _isHighPerformanceDevice = false;
    }
  }

  /// Analyze Android device capabilities
  void _analyzeAndroidDevice(AndroidDeviceInfo androidInfo) {
    // Check for high-performance indicators
    final hasHighRAM = (androidInfo.systemFeatures?.any(
      (feature) => feature.contains('ram') && feature.contains('6'),
    ) ?? false);
    
    final isRecentDevice = androidInfo.version.sdkInt >= 28; // Android 9+
    
    // Simple heuristic for performance classification
    _isHighPerformanceDevice = hasHighRAM && isRecentDevice;
    
    print('Android device analysis: High performance = $_isHighPerformanceDevice');
    print('SDK: ${androidInfo.version.sdkInt}, Model: ${androidInfo.model}');
  }

  /// Analyze iOS device capabilities
  void _analyzeIOSDevice(IosDeviceInfo iosInfo) {
    // Check for high-performance iOS devices
    final deviceModel = iosInfo.model.toLowerCase();
    final isRecentiPhone = deviceModel.contains('iphone') && 
        (deviceModel.contains('12') || deviceModel.contains('13') || 
         deviceModel.contains('14') || deviceModel.contains('15'));
    
    final isRecentiPad = deviceModel.contains('ipad') && 
        (deviceModel.contains('pro') || deviceModel.contains('air'));
    
    _isHighPerformanceDevice = isRecentiPhone || isRecentiPad;
    
    print('iOS device analysis: High performance = $_isHighPerformanceDevice');
    print('Model: ${iosInfo.model}, System: ${iosInfo.systemVersion}');
  }

  /// Optimize settings based on device capabilities
  void _optimizeSettings() {
    if (_isHighPerformanceDevice) {
      // High-performance device settings
      _recommendedFrameSkip = 1; // Process every 2nd frame
      _recommendedImageSize = 416; // Higher resolution
      _targetFps = 20.0; // Higher target FPS
    } else {
      // Conservative settings for lower-end devices
      _recommendedFrameSkip = 3; // Process every 4th frame
      _recommendedImageSize = 224; // Lower resolution
      _targetFps = 10.0; // Lower target FPS
    }
    
    print('Optimized settings: frameSkip=$_recommendedFrameSkip, '
          'imageSize=$_recommendedImageSize, targetFps=$_targetFps');
  }

  /// Start performance monitoring
  void _startPerformanceMonitoring() {
    _performanceMonitor = Timer.periodic(
      const Duration(seconds: 2),
      (_) => _updatePerformanceMetrics(),
    );
  }

  /// Update performance metrics
  void _updatePerformanceMetrics() {
    // Calculate average inference time
    if (_inferenceTimesMs.isNotEmpty) {
      _averageInferenceTime = _inferenceTimesMs.reduce((a, b) => a + b) / 
          _inferenceTimesMs.length;
      
      // Keep only recent measurements
      if (_inferenceTimesMs.length > 10) {
        _inferenceTimesMs.removeRange(0, _inferenceTimesMs.length - 10);
      }
    }
    
    // Calculate average FPS
    if (_fpsHistory.isNotEmpty) {
      _currentFps = _fpsHistory.reduce((a, b) => a + b) / _fpsHistory.length;
      
      // Keep only recent measurements
      if (_fpsHistory.length > 5) {
        _fpsHistory.removeRange(0, _fpsHistory.length - 5);
      }
    }
    
    // Adaptive optimization based on performance
    _adaptiveOptimization();
    
    notifyListeners();
  }

  /// Adaptive optimization based on current performance
  void _adaptiveOptimization() {
    // If FPS is too low, reduce quality
    if (_currentFps < _targetFps * 0.7) {
      if (_recommendedImageSize > 224) {
        _recommendedImageSize = (_recommendedImageSize * 0.9).round();
        print('Reducing image size to $_recommendedImageSize for better performance');
      }
      
      if (_recommendedFrameSkip < 4) {
        _recommendedFrameSkip++;
        print('Increasing frame skip to $_recommendedFrameSkip for better performance');
      }
    }
    
    // If FPS is consistently high, we can increase quality
    else if (_currentFps > _targetFps * 1.2 && _fpsHistory.length >= 3) {
      final allHighFps = _fpsHistory.every((fps) => fps > _targetFps * 1.1);
      
      if (allHighFps) {
        if (_recommendedImageSize < 416) {
          _recommendedImageSize = (_recommendedImageSize * 1.1).round();
          print('Increasing image size to $_recommendedImageSize');
        }
        
        if (_recommendedFrameSkip > 1) {
          _recommendedFrameSkip--;
          print('Decreasing frame skip to $_recommendedFrameSkip');
        }
      }
    }
  }

  /// Record inference time for performance tracking
  void recordInferenceTime(double timeMs) {
    _inferenceTimesMs.add(timeMs);
  }

  /// Record FPS for performance tracking
  void recordFps(double fps) {
    _fpsHistory.add(fps);
  }

  /// Get memory usage estimate
  Future<void> updateMemoryUsage() async {
    try {
      // This is a simplified memory tracking
      // In production, you might use more sophisticated memory monitoring
      _memoryUsageMB = (DateTime.now().millisecondsSinceEpoch % 100) + 50;
    } catch (e) {
      print('Error updating memory usage: $e');
    }
  }

  /// Dispose performance service
  void dispose() {
    _performanceMonitor?.cancel();
    super.dispose();
  }
}
