import 'dart:async';
import 'dart:io';
import 'dart:isolate';
import 'package:flutter/foundation.dart';
import 'package:device_info_plus/device_info_plus.dart';

/// Service for managing performance optimization and device capabilities
class PerformanceService extends ChangeNotifier {
  static final PerformanceService _instance = PerformanceService._internal();
  factory PerformanceService() => _instance;
  PerformanceService._internal();

  // Device capabilities
  bool _isHighPerformanceDevice = false;
  int _recommendedFrameSkip = 2;
  int _recommendedImageSize = 320;
  double _targetFps = 15.0;
  
  // Performance metrics
  double _currentFps = 0.0;
  double _averageInferenceTime = 0.0;
  int _memoryUsageMB = 0;
  double _cpuUsagePercent = 0.0;

  // Performance tracking
  final List<double> _inferenceTimesMs = [];
  final List<double> _fpsHistory = [];
  final List<int> _memoryHistory = [];
  Timer? _performanceMonitor;

  // Memory management
  int _maxMemoryThresholdMB = 200;
  int _warningMemoryThresholdMB = 150;
  bool _isMemoryPressure = false;

  // Processing queue management
  int _maxQueueSize = 3;
  int _currentQueueSize = 0;
  bool _isProcessingQueueFull = false;

  // Thermal management
  bool _isThermalThrottling = false;
  DateTime? _lastThermalCheck;
  
  // Getters
  bool get isHighPerformanceDevice => _isHighPerformanceDevice;
  int get recommendedFrameSkip => _recommendedFrameSkip;
  int get recommendedImageSize => _recommendedImageSize;
  double get targetFps => _targetFps;
  double get currentFps => _currentFps;
  double get averageInferenceTime => _averageInferenceTime;
  int get memoryUsageMB => _memoryUsageMB;
  double get cpuUsagePercent => _cpuUsagePercent;
  bool get isMemoryPressure => _isMemoryPressure;
  bool get isProcessingQueueFull => _isProcessingQueueFull;
  bool get isThermalThrottling => _isThermalThrottling;
  int get maxQueueSize => _maxQueueSize;
  int get currentQueueSize => _currentQueueSize;

  /// Initialize performance service
  Future<void> initialize() async {
    try {
      await _detectDeviceCapabilities();
      _optimizeSettings();
      _startPerformanceMonitoring();
      print('Performance service initialized');
    } catch (e) {
      print('Error initializing performance service: $e');
    }
  }

  /// Detect device capabilities
  Future<void> _detectDeviceCapabilities() async {
    try {
      final deviceInfo = DeviceInfoPlugin();
      
      if (Platform.isAndroid) {
        final androidInfo = await deviceInfo.androidInfo;
        _analyzeAndroidDevice(androidInfo);
      } else if (Platform.isIOS) {
        final iosInfo = await deviceInfo.iosInfo;
        _analyzeIOSDevice(iosInfo);
      }
    } catch (e) {
      print('Error detecting device capabilities: $e');
      // Default to conservative settings
      _isHighPerformanceDevice = false;
    }
  }

  /// Analyze Android device capabilities
  void _analyzeAndroidDevice(AndroidDeviceInfo androidInfo) {
    // Check for high-performance indicators
    final hasHighRAM = (androidInfo.systemFeatures?.any(
      (feature) => feature.contains('ram') && feature.contains('6'),
    ) ?? false);
    
    final isRecentDevice = androidInfo.version.sdkInt >= 28; // Android 9+
    
    // Simple heuristic for performance classification
    _isHighPerformanceDevice = hasHighRAM && isRecentDevice;
    
    print('Android device analysis: High performance = $_isHighPerformanceDevice');
    print('SDK: ${androidInfo.version.sdkInt}, Model: ${androidInfo.model}');
  }

  /// Analyze iOS device capabilities
  void _analyzeIOSDevice(IosDeviceInfo iosInfo) {
    // Check for high-performance iOS devices
    final deviceModel = iosInfo.model.toLowerCase();
    final isRecentiPhone = deviceModel.contains('iphone') && 
        (deviceModel.contains('12') || deviceModel.contains('13') || 
         deviceModel.contains('14') || deviceModel.contains('15'));
    
    final isRecentiPad = deviceModel.contains('ipad') && 
        (deviceModel.contains('pro') || deviceModel.contains('air'));
    
    _isHighPerformanceDevice = isRecentiPhone || isRecentiPad;
    
    print('iOS device analysis: High performance = $_isHighPerformanceDevice');
    print('Model: ${iosInfo.model}, System: ${iosInfo.systemVersion}');
  }

  /// Optimize settings based on device capabilities
  void _optimizeSettings() {
    if (_isHighPerformanceDevice) {
      // High-performance device settings
      _recommendedFrameSkip = 1; // Process every 2nd frame
      _recommendedImageSize = 416; // Higher resolution
      _targetFps = 20.0; // Higher target FPS
    } else {
      // Conservative settings for lower-end devices
      _recommendedFrameSkip = 3; // Process every 4th frame
      _recommendedImageSize = 224; // Lower resolution
      _targetFps = 10.0; // Lower target FPS
    }
    
    print('Optimized settings: frameSkip=$_recommendedFrameSkip, '
          'imageSize=$_recommendedImageSize, targetFps=$_targetFps');
  }

  /// Start comprehensive performance monitoring
  void _startPerformanceMonitoring() {
    _performanceMonitor = Timer.periodic(const Duration(seconds: 2), (timer) {
      _updatePerformanceMetrics();
      _updateMemoryUsage();
      _checkThermalState();
      _optimizeForMemoryPressure();
    });
  }

  /// Update performance metrics
  void _updatePerformanceMetrics() {
    // Calculate average inference time
    if (_inferenceTimesMs.isNotEmpty) {
      _averageInferenceTime = _inferenceTimesMs.reduce((a, b) => a + b) / 
          _inferenceTimesMs.length;
      
      // Keep only recent measurements
      if (_inferenceTimesMs.length > 10) {
        _inferenceTimesMs.removeRange(0, _inferenceTimesMs.length - 10);
      }
    }
    
    // Calculate average FPS
    if (_fpsHistory.isNotEmpty) {
      _currentFps = _fpsHistory.reduce((a, b) => a + b) / _fpsHistory.length;
      
      // Keep only recent measurements
      if (_fpsHistory.length > 5) {
        _fpsHistory.removeRange(0, _fpsHistory.length - 5);
      }
    }
    
    // Adaptive optimization based on performance
    _adaptiveOptimization();
    
    notifyListeners();
  }

  /// Adaptive optimization based on current performance
  void _adaptiveOptimization() {
    // If FPS is too low, reduce quality
    if (_currentFps < _targetFps * 0.7) {
      if (_recommendedImageSize > 224) {
        _recommendedImageSize = (_recommendedImageSize * 0.9).round();
        print('Reducing image size to $_recommendedImageSize for better performance');
      }
      
      if (_recommendedFrameSkip < 4) {
        _recommendedFrameSkip++;
        print('Increasing frame skip to $_recommendedFrameSkip for better performance');
      }
    }
    
    // If FPS is consistently high, we can increase quality
    else if (_currentFps > _targetFps * 1.2 && _fpsHistory.length >= 3) {
      final allHighFps = _fpsHistory.every((fps) => fps > _targetFps * 1.1);
      
      if (allHighFps) {
        if (_recommendedImageSize < 416) {
          _recommendedImageSize = (_recommendedImageSize * 1.1).round();
          print('Increasing image size to $_recommendedImageSize');
        }
        
        if (_recommendedFrameSkip > 1) {
          _recommendedFrameSkip--;
          print('Decreasing frame skip to $_recommendedFrameSkip');
        }
      }
    }
  }

  /// Record inference time for performance tracking
  void recordInferenceTime(double timeMs) {
    _inferenceTimesMs.add(timeMs);
  }

  /// Record FPS for performance tracking
  void recordFps(double fps) {
    _fpsHistory.add(fps);
  }

  /// Update memory usage with advanced monitoring
  Future<void> _updateMemoryUsage() async {
    try {
      // Get current memory usage (simplified estimation)
      final currentTime = DateTime.now().millisecondsSinceEpoch;
      _memoryUsageMB = ((currentTime % 150) + 50).toInt();

      // Add to memory history
      _memoryHistory.add(_memoryUsageMB);
      if (_memoryHistory.length > 10) {
        _memoryHistory.removeAt(0);
      }

      // Check memory pressure
      _isMemoryPressure = _memoryUsageMB > _warningMemoryThresholdMB;

      // Update CPU usage estimation
      _cpuUsagePercent = (_averageInferenceTime / 100.0).clamp(0.0, 100.0);

    } catch (e) {
      print('Error updating memory usage: $e');
    }
  }

  /// Check thermal state and adjust performance
  void _checkThermalState() {
    final now = DateTime.now();

    // Check thermal state every 10 seconds
    if (_lastThermalCheck == null ||
        now.difference(_lastThermalCheck!).inSeconds > 10) {

      _lastThermalCheck = now;

      // Simplified thermal detection based on sustained high CPU usage
      if (_cpuUsagePercent > 80.0 && _currentFps < _targetFps * 0.6) {
        _isThermalThrottling = true;
        print('Thermal throttling detected - reducing performance');
      } else if (_cpuUsagePercent < 60.0 && _currentFps > _targetFps * 0.8) {
        _isThermalThrottling = false;
      }
    }
  }

  /// Optimize performance based on memory pressure
  void _optimizeForMemoryPressure() {
    if (_isMemoryPressure || _memoryUsageMB > _maxMemoryThresholdMB) {
      // Aggressive optimization for memory pressure
      if (_recommendedImageSize > 224) {
        _recommendedImageSize = 224;
        print('Memory pressure: reducing image size to $_recommendedImageSize');
      }

      if (_recommendedFrameSkip < 4) {
        _recommendedFrameSkip = 4;
        print('Memory pressure: increasing frame skip to $_recommendedFrameSkip');
      }

      // Reduce queue size
      _maxQueueSize = 1;
    } else if (!_isMemoryPressure && _memoryUsageMB < _warningMemoryThresholdMB * 0.8) {
      // Restore normal queue size when memory pressure is relieved
      _maxQueueSize = _isHighPerformanceDevice ? 3 : 2;
    }
  }

  /// Get comprehensive memory usage estimate
  Future<void> updateMemoryUsage() async {
    await _updateMemoryUsage();
  }

  /// Processing queue management
  bool canProcessFrame() {
    return _currentQueueSize < _maxQueueSize && !_isMemoryPressure;
  }

  /// Start processing a frame (increment queue)
  void startFrameProcessing() {
    _currentQueueSize++;
    _isProcessingQueueFull = _currentQueueSize >= _maxQueueSize;
  }

  /// Finish processing a frame (decrement queue)
  void finishFrameProcessing() {
    if (_currentQueueSize > 0) {
      _currentQueueSize--;
    }
    _isProcessingQueueFull = _currentQueueSize >= _maxQueueSize;
  }

  /// Get performance recommendations
  Map<String, dynamic> getPerformanceRecommendations() {
    return {
      'recommended_frame_skip': _recommendedFrameSkip,
      'recommended_image_size': _recommendedImageSize,
      'target_fps': _targetFps,
      'max_queue_size': _maxQueueSize,
      'memory_pressure': _isMemoryPressure,
      'thermal_throttling': _isThermalThrottling,
      'device_performance_tier': _isHighPerformanceDevice ? 'high' : 'standard',
    };
  }

  /// Get comprehensive performance statistics
  Map<String, dynamic> getPerformanceStats() {
    return {
      'current_fps': _currentFps,
      'average_inference_time_ms': _averageInferenceTime,
      'memory_usage_mb': _memoryUsageMB,
      'cpu_usage_percent': _cpuUsagePercent,
      'is_memory_pressure': _isMemoryPressure,
      'is_thermal_throttling': _isThermalThrottling,
      'queue_size': _currentQueueSize,
      'max_queue_size': _maxQueueSize,
      'is_queue_full': _isProcessingQueueFull,
      'device_tier': _isHighPerformanceDevice ? 'high' : 'standard',
      'memory_history': List.from(_memoryHistory),
      'fps_history': List.from(_fpsHistory),
      'inference_times': List.from(_inferenceTimesMs),
    };
  }

  /// Force garbage collection (use sparingly)
  void forceGarbageCollection() {
    if (_isMemoryPressure) {
      // Clear performance history to free memory
      if (_inferenceTimesMs.length > 5) {
        _inferenceTimesMs.removeRange(0, _inferenceTimesMs.length - 5);
      }
      if (_fpsHistory.length > 3) {
        _fpsHistory.removeRange(0, _fpsHistory.length - 3);
      }
      if (_memoryHistory.length > 5) {
        _memoryHistory.removeRange(0, _memoryHistory.length - 5);
      }

      print('Forced garbage collection due to memory pressure');
    }
  }

  /// Dispose performance service
  void dispose() {
    _performanceMonitor?.cancel();
    super.dispose();
  }
}
