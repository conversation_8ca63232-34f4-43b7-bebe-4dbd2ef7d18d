import 'dart:async';
import 'dart:isolate';
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:camera/camera.dart';
import 'package:flutter/foundation.dart';
import 'package:image/image.dart' as img;
import '../ml/detection_model.dart';
import 'camera_service.dart';
import 'performance_service.dart';
import 'storage_service.dart';

/// Service for managing real-time object detection
class DetectionService extends ChangeNotifier {
  final CameraService _cameraService;
  final DetectionModel _detectionModel;
  final PerformanceService _performanceService;
  
  // Detection state
  bool _isDetecting = false;
  List<Detection> _currentDetections = [];
  String? _error;
  double _processingFps = 0.0;
  int _totalDetections = 0;
  
  // Performance tracking
  DateTime? _lastProcessTime;
  int _frameCount = 0;
  Timer? _fpsTimer;
  
  // Frame processing control
  bool _isProcessingFrame = false;
  int _frameSkipCount = 0;
  int _maxFrameSkip = 2; // Dynamic frame skip based on performance
  
  StreamSubscription<CameraImage>? _frameSubscription;

  DetectionService(this._cameraService, this._detectionModel, this._performanceService);

  // Getters
  bool get isDetecting => _isDetecting;
  List<Detection> get currentDetections => List.unmodifiable(_currentDetections);
  String? get error => _error;
  double get processingFps => _processingFps;
  int get totalDetections => _totalDetections;
  bool get isInitialized => _cameraService.isInitialized && _detectionModel.isInitialized;

  /// Initialize detection service
  Future<bool> initialize() async {
    try {
      _error = null;

      // Initialize performance service
      await _performanceService.initialize();

      // Initialize camera service
      final cameraInitialized = await _cameraService.initialize();
      if (!cameraInitialized) {
        _error = 'Failed to initialize camera';
        notifyListeners();
        return false;
      }

      // Initialize detection model
      final modelInitialized = await _detectionModel.initialize();
      if (!modelInitialized) {
        _error = 'Failed to initialize detection model';
        notifyListeners();
        return false;
      }

      // Update frame skip based on device performance
      _maxFrameSkip = _performanceService.recommendedFrameSkip;

      // Start FPS tracking
      _startFpsTracking();

      print('Detection service initialized successfully');
      notifyListeners();
      return true;
    } catch (e) {
      _error = 'Failed to initialize detection service: $e';
      print(_error);
      notifyListeners();
      return false;
    }
  }

  /// Start real-time detection
  void startDetection() {
    if (!isInitialized || _isDetecting) return;
    
    _isDetecting = true;
    _error = null;
    
    // Start camera frame stream
    _cameraService.startFrameStream();
    
    // Subscribe to frame stream
    _frameSubscription = _cameraService.frameStream?.listen(
      _processFrame,
      onError: (error) {
        _error = 'Frame processing error: $error';
        print(_error);
        notifyListeners();
      },
    );
    
    notifyListeners();
    print('Started real-time detection');
  }

  /// Stop real-time detection
  Future<void> stopDetection() async {
    if (!_isDetecting) return;
    
    _isDetecting = false;
    
    // Cancel frame subscription
    await _frameSubscription?.cancel();
    _frameSubscription = null;
    
    // Stop camera frame stream
    await _cameraService.stopFrameStream();
    
    // Clear current detections
    _currentDetections.clear();
    
    notifyListeners();
    print('Stopped real-time detection');
  }

  /// Process a single camera frame with advanced performance optimization
  Future<void> _processFrame(CameraImage cameraImage) async {
    // Check if we can process this frame based on performance constraints
    if (_isProcessingFrame || !_performanceService.canProcessFrame()) {
      return;
    }

    // Dynamic frame skipping based on performance
    _frameSkipCount++;
    _maxFrameSkip = _performanceService.recommendedFrameSkip;
    if (_frameSkipCount < _maxFrameSkip) return;
    _frameSkipCount = 0;

    // Check for memory pressure and thermal throttling
    if (_performanceService.isMemoryPressure || _performanceService.isThermalThrottling) {
      // Skip additional frames under stress
      if (_frameSkipCount % 2 != 0) return;
    }

    _isProcessingFrame = true;
    _performanceService.startFrameProcessing();
    final startTime = DateTime.now();

    try {
      // Process frame with performance-optimized pipeline
      final detections = await _processFrameOptimized(cameraImage);

      // Record inference time for performance tracking
      final inferenceTime = DateTime.now().difference(startTime).inMilliseconds.toDouble();
      _performanceService.recordInferenceTime(inferenceTime);

      // Update current detections
      _currentDetections = detections;
      _totalDetections += detections.length;

      // Update FPS tracking
      _frameCount++;
      _lastProcessTime = DateTime.now();

      // Force garbage collection if under memory pressure
      if (_performanceService.isMemoryPressure && _frameCount % 20 == 0) {
        _performanceService.forceGarbageCollection();
      }

      notifyListeners();
    } catch (e) {
      _error = 'Detection processing error: $e';
      print(_error);
    } finally {
      _isProcessingFrame = false;
      _performanceService.finishFrameProcessing();
    }
  }

  /// Advanced optimized frame processing pipeline with performance management
  Future<List<Detection>> _processFrameOptimized(CameraImage cameraImage) async {
    try {
      // Get dynamic image size based on performance
      final targetSize = _performanceService.recommendedImageSize;

      // Convert CameraImage to optimized format with dynamic sizing
      final processedImage = await _convertAndResizeCameraImage(
        cameraImage,
        targetSize: targetSize,
      );
      if (processedImage == null) return [];

      // Create UI Image for detection
      final uiImage = await _createUIImageFromBytes(processedImage);
      if (uiImage == null) return [];

      // Run detection with performance monitoring
      final detectionStartTime = DateTime.now();
      final detections = await _detectionModel.detectObjects(uiImage);
      final detectionTime = DateTime.now().difference(detectionStartTime).inMilliseconds;

      // Log performance if needed
      if (detectionTime > 200) {
        print('Slow detection: ${detectionTime}ms for ${targetSize}px image');
      }

      // Clean up immediately to free memory
      uiImage.dispose();

      // Filter detections based on performance mode
      return _filterDetectionsForPerformance(detections);
    } catch (e) {
      print('Optimized frame processing error: $e');
      return [];
    }
  }

  /// Filter detections based on performance constraints
  List<Detection> _filterDetectionsForPerformance(List<Detection> detections) {
    // Under memory pressure, limit number of detections
    if (_performanceService.isMemoryPressure) {
      // Keep only high-confidence detections
      final filtered = detections.where((d) => d.confidence > 0.7).toList();
      // Limit to top 5 detections
      filtered.sort((a, b) => b.confidence.compareTo(a.confidence));
      return filtered.take(5).toList();
    }

    // Under thermal throttling, be more selective
    if (_performanceService.isThermalThrottling) {
      final filtered = detections.where((d) => d.confidence > 0.6).toList();
      filtered.sort((a, b) => b.confidence.compareTo(a.confidence));
      return filtered.take(8).toList();
    }

    // Normal operation - return all detections
    return detections;
  }

  /// Convert and resize camera image for optimal processing with dynamic sizing
  Future<Uint8List?> _convertAndResizeCameraImage(
    CameraImage cameraImage, {
    int? targetSize,
  }) async {
    try {
      img.Image? image;

      // Handle different image formats efficiently
      if (cameraImage.format.group == ImageFormatGroup.yuv420) {
        image = await _convertYUV420Fast(cameraImage);
      } else if (cameraImage.format.group == ImageFormatGroup.bgra8888) {
        image = await _convertBGRAFast(cameraImage);
      } else {
        // Fallback to camera service conversion
        final uiImage = await _cameraService.convertCameraImageToUIImage(cameraImage);
        if (uiImage == null) return null;

        final byteData = await uiImage.toByteData(format: ui.ImageByteFormat.png);
        uiImage.dispose();

        return byteData?.buffer.asUint8List();
      }

      if (image == null) return null;

      // Resize for optimal inference with dynamic sizing
      final dynamicTargetSize = targetSize ?? _performanceService.recommendedImageSize;
      if (image.width > dynamicTargetSize || image.height > dynamicTargetSize) {
        image = img.copyResize(
          image,
          width: dynamicTargetSize,
          height: dynamicTargetSize,
          interpolation: img.Interpolation.nearest, // Faster than linear
        );
      }

      // Convert to PNG bytes
      return Uint8List.fromList(img.encodePng(image));
    } catch (e) {
      print('Image conversion error: $e');
      return null;
    }
  }

  /// Fast YUV420 to RGB conversion with optimizations
  Future<img.Image?> _convertYUV420Fast(CameraImage cameraImage) async {
    try {
      final int width = cameraImage.width;
      final int height = cameraImage.height;

      final Uint8List yPlane = cameraImage.planes[0].bytes;
      final Uint8List uPlane = cameraImage.planes[1].bytes;
      final Uint8List vPlane = cameraImage.planes[2].bytes;

      final int uvRowStride = cameraImage.planes[1].bytesPerRow;
      final int uvPixelStride = cameraImage.planes[1].bytesPerPixel ?? 1;

      // Create RGB bytes directly for better performance
      final Uint8List rgbBytes = Uint8List(width * height * 3);
      int rgbIndex = 0;

      for (int y = 0; y < height; y++) {
        for (int x = 0; x < width; x++) {
          final int yIndex = y * width + x;
          final int uvIndex = (y ~/ 2) * uvRowStride + (x ~/ 2) * uvPixelStride;

          final int yValue = yPlane[yIndex];
          final int uValue = uPlane[uvIndex];
          final int vValue = vPlane[uvIndex];

          // Optimized YUV to RGB conversion
          final int r = (yValue + ((vValue - 128) * 1.402)).round().clamp(0, 255);
          final int g = (yValue - ((uValue - 128) * 0.344) - ((vValue - 128) * 0.714)).round().clamp(0, 255);
          final int b = (yValue + ((uValue - 128) * 1.772)).round().clamp(0, 255);

          rgbBytes[rgbIndex++] = r;
          rgbBytes[rgbIndex++] = g;
          rgbBytes[rgbIndex++] = b;
        }
      }

      return img.Image.fromBytes(
        width: width,
        height: height,
        bytes: rgbBytes.buffer,
        format: img.Format.uint8,
        numChannels: 3,
      );
    } catch (e) {
      print('Fast YUV420 conversion error: $e');
      return null;
    }
  }

  /// Fast BGRA to RGB conversion
  Future<img.Image?> _convertBGRAFast(CameraImage cameraImage) async {
    try {
      final Uint8List bytes = cameraImage.planes[0].bytes;

      // Create RGB bytes from BGRA
      final int pixelCount = cameraImage.width * cameraImage.height;
      final Uint8List rgbBytes = Uint8List(pixelCount * 3);

      for (int i = 0; i < pixelCount; i++) {
        final int bgraIndex = i * 4;
        final int rgbIndex = i * 3;

        // BGRA to RGB conversion
        rgbBytes[rgbIndex] = bytes[bgraIndex + 2];     // R
        rgbBytes[rgbIndex + 1] = bytes[bgraIndex + 1]; // G
        rgbBytes[rgbIndex + 2] = bytes[bgraIndex];     // B
      }

      return img.Image.fromBytes(
        width: cameraImage.width,
        height: cameraImage.height,
        bytes: rgbBytes.buffer,
        format: img.Format.uint8,
        numChannels: 3,
      );
    } catch (e) {
      print('Fast BGRA conversion error: $e');
      return null;
    }
  }

  /// Create UI Image from processed bytes
  Future<ui.Image?> _createUIImageFromBytes(Uint8List bytes) async {
    try {
      final ui.Codec codec = await ui.instantiateImageCodec(bytes);
      final ui.FrameInfo frameInfo = await codec.getNextFrame();
      return frameInfo.image;
    } catch (e) {
      print('UI Image creation error: $e');
      return null;
    }
  }

  /// Start advanced FPS tracking with comprehensive performance monitoring
  void _startFpsTracking() {
    _fpsTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      _processingFps = _frameCount.toDouble();

      // Record FPS for performance service
      _performanceService.recordFps(_processingFps);

      // Update dynamic frame skip based on performance
      _maxFrameSkip = _performanceService.recommendedFrameSkip;

      // Log performance warnings
      if (_processingFps < _performanceService.targetFps * 0.5) {
        print('Performance warning: FPS ($_processingFps) significantly below target (${_performanceService.targetFps})');
      }

      // Adaptive quality adjustment
      if (_performanceService.isMemoryPressure || _performanceService.isThermalThrottling) {
        print('Performance constraints active - Memory: ${_performanceService.isMemoryPressure}, Thermal: ${_performanceService.isThermalThrottling}');
      }

      _frameCount = 0;
      notifyListeners();
    });
  }

  /// Toggle flash
  Future<void> toggleFlash() async {
    await _cameraService.toggleFlash();
    notifyListeners();
  }

  /// Switch camera
  Future<void> switchCamera() async {
    final wasDetecting = _isDetecting;
    
    if (wasDetecting) {
      await stopDetection();
    }
    
    await _cameraService.switchCamera();
    
    if (wasDetecting) {
      startDetection();
    }
    
    notifyListeners();
  }

  /// Set zoom level
  Future<void> setZoomLevel(double zoom) async {
    await _cameraService.setZoomLevel(zoom);
    notifyListeners();
  }

  /// Capture photo with current detections and save to storage
  Future<Map<String, dynamic>?> captureSnapshot() async {
    try {
      final photo = await _cameraService.capturePhoto();
      if (photo == null) return null;

      final now = DateTime.now();
      final snapshotId = now.millisecondsSinceEpoch.toString();

      // Calculate average confidence
      final averageConfidence = _currentDetections.isNotEmpty
          ? _currentDetections.map((d) => d.confidence).reduce((a, b) => a + b) / _currentDetections.length
          : 0.0;

      // Create snapshot data for storage
      final snapshotData = {
        'id': snapshotId,
        'image_path': photo.path,
        'timestamp': now.toIso8601String(),
        'total_detections': _currentDetections.length,
        'average_confidence': averageConfidence,
        'metadata': _createSnapshotMetadata(),
        'detections': _currentDetections.map((d) => {
          'label': d.label,
          'confidence': d.confidence,
          'x': d.x,
          'y': d.y,
          'width': d.width,
          'height': d.height,
          'timestamp': d.timestamp.toIso8601String(),
        }).toList(),
      };

      // Save to storage
      final saved = await StorageService.saveSnapshot(snapshotData);
      if (!saved) {
        print('Warning: Failed to save snapshot to storage');
      }

      // Return snapshot data for immediate use
      final snapshot = {
        'id': snapshotId,
        'imagePath': photo.path,
        'detections': _currentDetections.map((d) => d.toMap()).toList(),
        'timestamp': now.toIso8601String(),
        'totalDetections': _currentDetections.length,
        'averageConfidence': averageConfidence,
        'saved': saved,
      };

      print('Captured snapshot with ${_currentDetections.length} detections, saved: $saved');
      return snapshot;
    } catch (e) {
      _error = 'Failed to capture snapshot: $e';
      print(_error);
      notifyListeners();
      return null;
    }
  }

  /// Create metadata for snapshot
  String _createSnapshotMetadata() {
    final metadata = {
      'device_performance': _performanceService.isHighPerformanceDevice,
      'processing_fps': _processingFps,
      'inference_time': _performanceService.averageInferenceTime,
      'frame_skip': _maxFrameSkip,
      'image_size': _performanceService.recommendedImageSize,
      'camera_settings': {
        'zoom': _cameraService.currentZoomLevel,
        'flash': _cameraService.isFlashOn,
      },
    };

    // Convert to JSON string
    try {
      return metadata.toString(); // Simple string representation for now
    } catch (e) {
      return '{}';
    }
  }

  /// Get detection by coordinates (for tap detection)
  Detection? getDetectionAtPoint(double x, double y, double screenWidth, double screenHeight) {
    for (final detection in _currentDetections) {
      // Convert detection coordinates to screen coordinates
      final left = detection.x;
      final top = detection.y;
      final right = detection.x + detection.width;
      final bottom = detection.y + detection.height;
      
      if (x >= left && x <= right && y >= top && y <= bottom) {
        return detection;
      }
    }
    return null;
  }

  /// Get comprehensive performance statistics
  Map<String, dynamic> getPerformanceStats() {
    final performanceStats = _performanceService.getPerformanceStats();

    return {
      // Detection-specific stats
      'processingFps': _processingFps,
      'totalDetections': _totalDetections,
      'currentDetectionCount': _currentDetections.length,
      'isProcessing': _isProcessingFrame,
      'lastProcessTime': _lastProcessTime?.toIso8601String(),
      'frameSkipCount': _maxFrameSkip,

      // Camera stats
      'cameraZoom': _cameraService.zoomLevel,
      'isFlashOn': _cameraService.isFlashOn,
      'isRearCamera': _cameraService.isRearCamera,

      // Performance service stats
      'averageInferenceTime': performanceStats['average_inference_time_ms'],
      'memoryUsageMB': performanceStats['memory_usage_mb'],
      'cpuUsagePercent': performanceStats['cpu_usage_percent'],
      'isMemoryPressure': performanceStats['is_memory_pressure'],
      'isThermalThrottling': performanceStats['is_thermal_throttling'],
      'queueSize': performanceStats['queue_size'],
      'maxQueueSize': performanceStats['max_queue_size'],
      'isQueueFull': performanceStats['is_queue_full'],
      'deviceTier': performanceStats['device_tier'],
      'recommendedImageSize': _performanceService.recommendedImageSize,
      'targetFps': _performanceService.targetFps,

      // Performance history (last 5 measurements)
      'fpsHistory': performanceStats['fps_history'],
      'memoryHistory': performanceStats['memory_history'],
      'inferenceTimeHistory': performanceStats['inference_times'],
    };
  }

  /// Pause detection (for app lifecycle)
  Future<void> pause() async {
    if (_isDetecting) {
      await stopDetection();
    }
    await _cameraService.pause();
  }

  /// Resume detection (for app lifecycle)
  Future<void> resume() async {
    await _cameraService.resume();
    if (isInitialized) {
      startDetection();
    }
  }

  /// Dispose detection service
  @override
  void dispose() {
    stopDetection();
    _fpsTimer?.cancel();
    _detectionModel.dispose();
    _cameraService.dispose();
    super.dispose();
  }
}
