import 'dart:async';
import 'dart:ui' as ui;
import 'package:camera/camera.dart';
import 'package:flutter/foundation.dart';
import '../ml/detection_model.dart';
import 'camera_service.dart';

/// Service for managing real-time object detection
class DetectionService extends ChangeNotifier {
  final CameraService _cameraService;
  final DetectionModel _detectionModel;
  
  // Detection state
  bool _isDetecting = false;
  List<Detection> _currentDetections = [];
  String? _error;
  double _processingFps = 0.0;
  int _totalDetections = 0;
  
  // Performance tracking
  DateTime? _lastProcessTime;
  int _frameCount = 0;
  Timer? _fpsTimer;
  
  // Frame processing control
  bool _isProcessingFrame = false;
  int _frameSkipCount = 0;
  static const int maxFrameSkip = 2; // Process every 3rd frame for performance
  
  StreamSubscription<CameraImage>? _frameSubscription;

  DetectionService(this._cameraService, this._detectionModel);

  // Getters
  bool get isDetecting => _isDetecting;
  List<Detection> get currentDetections => List.unmodifiable(_currentDetections);
  String? get error => _error;
  double get processingFps => _processingFps;
  int get totalDetections => _totalDetections;
  bool get isInitialized => _cameraService.isInitialized && _detectionModel.isInitialized;

  /// Initialize detection service
  Future<bool> initialize() async {
    try {
      _error = null;
      
      // Initialize camera service
      final cameraInitialized = await _cameraService.initialize();
      if (!cameraInitialized) {
        _error = 'Failed to initialize camera';
        notifyListeners();
        return false;
      }

      // Initialize detection model
      final modelInitialized = await _detectionModel.initialize();
      if (!modelInitialized) {
        _error = 'Failed to initialize detection model';
        notifyListeners();
        return false;
      }

      // Start FPS tracking
      _startFpsTracking();
      
      print('Detection service initialized successfully');
      notifyListeners();
      return true;
    } catch (e) {
      _error = 'Failed to initialize detection service: $e';
      print(_error);
      notifyListeners();
      return false;
    }
  }

  /// Start real-time detection
  void startDetection() {
    if (!isInitialized || _isDetecting) return;
    
    _isDetecting = true;
    _error = null;
    
    // Start camera frame stream
    _cameraService.startFrameStream();
    
    // Subscribe to frame stream
    _frameSubscription = _cameraService.frameStream?.listen(
      _processFrame,
      onError: (error) {
        _error = 'Frame processing error: $error';
        print(_error);
        notifyListeners();
      },
    );
    
    notifyListeners();
    print('Started real-time detection');
  }

  /// Stop real-time detection
  Future<void> stopDetection() async {
    if (!_isDetecting) return;
    
    _isDetecting = false;
    
    // Cancel frame subscription
    await _frameSubscription?.cancel();
    _frameSubscription = null;
    
    // Stop camera frame stream
    await _cameraService.stopFrameStream();
    
    // Clear current detections
    _currentDetections.clear();
    
    notifyListeners();
    print('Stopped real-time detection');
  }

  /// Process a single camera frame
  Future<void> _processFrame(CameraImage cameraImage) async {
    // Skip frames if already processing or for performance
    if (_isProcessingFrame) return;
    
    _frameSkipCount++;
    if (_frameSkipCount < maxFrameSkip) return;
    _frameSkipCount = 0;
    
    _isProcessingFrame = true;
    
    try {
      // Convert camera image to UI image
      final uiImage = await _cameraService.convertCameraImageToUIImage(cameraImage);
      if (uiImage == null) {
        _isProcessingFrame = false;
        return;
      }
      
      // Run object detection
      final detections = await _detectionModel.detectObjects(uiImage);
      
      // Update current detections
      _currentDetections = detections;
      _totalDetections += detections.length;
      
      // Update FPS tracking
      _frameCount++;
      _lastProcessTime = DateTime.now();
      
      // Dispose UI image
      uiImage.dispose();
      
      notifyListeners();
    } catch (e) {
      _error = 'Detection processing error: $e';
      print(_error);
    } finally {
      _isProcessingFrame = false;
    }
  }

  /// Start FPS tracking
  void _startFpsTracking() {
    _fpsTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      _processingFps = _frameCount.toDouble();
      _frameCount = 0;
      notifyListeners();
    });
  }

  /// Toggle flash
  Future<void> toggleFlash() async {
    await _cameraService.toggleFlash();
    notifyListeners();
  }

  /// Switch camera
  Future<void> switchCamera() async {
    final wasDetecting = _isDetecting;
    
    if (wasDetecting) {
      await stopDetection();
    }
    
    await _cameraService.switchCamera();
    
    if (wasDetecting) {
      startDetection();
    }
    
    notifyListeners();
  }

  /// Set zoom level
  Future<void> setZoomLevel(double zoom) async {
    await _cameraService.setZoomLevel(zoom);
    notifyListeners();
  }

  /// Capture photo with current detections
  Future<Map<String, dynamic>?> captureSnapshot() async {
    try {
      final photo = await _cameraService.capturePhoto();
      if (photo == null) return null;
      
      final snapshot = {
        'id': DateTime.now().millisecondsSinceEpoch.toString(),
        'imagePath': photo.path,
        'detections': _currentDetections.map((d) => d.toMap()).toList(),
        'timestamp': DateTime.now().toIso8601String(),
        'totalDetections': _currentDetections.length,
        'averageConfidence': _currentDetections.isNotEmpty
            ? _currentDetections.map((d) => d.confidence).reduce((a, b) => a + b) / _currentDetections.length
            : 0.0,
      };
      
      return snapshot;
    } catch (e) {
      _error = 'Failed to capture snapshot: $e';
      print(_error);
      notifyListeners();
      return null;
    }
  }

  /// Get detection by coordinates (for tap detection)
  Detection? getDetectionAtPoint(double x, double y, double screenWidth, double screenHeight) {
    for (final detection in _currentDetections) {
      // Convert detection coordinates to screen coordinates
      final left = detection.x;
      final top = detection.y;
      final right = detection.x + detection.width;
      final bottom = detection.y + detection.height;
      
      if (x >= left && x <= right && y >= top && y <= bottom) {
        return detection;
      }
    }
    return null;
  }

  /// Get performance statistics
  Map<String, dynamic> getPerformanceStats() {
    return {
      'processingFps': _processingFps,
      'totalDetections': _totalDetections,
      'currentDetectionCount': _currentDetections.length,
      'isProcessing': _isProcessingFrame,
      'lastProcessTime': _lastProcessTime?.toIso8601String(),
      'cameraZoom': _cameraService.zoomLevel,
      'isFlashOn': _cameraService.isFlashOn,
      'isRearCamera': _cameraService.isRearCamera,
    };
  }

  /// Pause detection (for app lifecycle)
  Future<void> pause() async {
    if (_isDetecting) {
      await stopDetection();
    }
    await _cameraService.pause();
  }

  /// Resume detection (for app lifecycle)
  Future<void> resume() async {
    await _cameraService.resume();
    if (isInitialized) {
      startDetection();
    }
  }

  /// Dispose detection service
  @override
  void dispose() {
    stopDetection();
    _fpsTimer?.cancel();
    _detectionModel.dispose();
    _cameraService.dispose();
    super.dispose();
  }
}
