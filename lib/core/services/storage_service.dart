import 'dart:convert';
import 'dart:io';
import 'package:path/path.dart';
import 'package:sqflite/sqflite.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:path_provider/path_provider.dart';
import '../ml/detection_model.dart';

/// Service for managing local data storage
class StorageService {
  static Database? _database;
  static SharedPreferences? _prefs;
  
  // Database configuration
  static const String _databaseName = 'visionary.db';
  static const int _databaseVersion = 1;
  
  // Table names
  static const String _detectionsTable = 'detections';
  static const String _snapshotsTable = 'snapshots';
  
  /// Initialize storage service
  static Future<bool> initialize() async {
    try {
      // Initialize SharedPreferences
      _prefs = await SharedPreferences.getInstance();
      
      // Initialize SQLite database
      _database = await _initializeDatabase();
      
      print('Storage service initialized successfully');
      return true;
    } catch (e) {
      print('Error initializing storage service: $e');
      return false;
    }
  }

  /// Initialize SQLite database
  static Future<Database> _initializeDatabase() async {
    final documentsDirectory = await getApplicationDocumentsDirectory();
    final path = join(documentsDirectory.path, _databaseName);
    
    return await openDatabase(
      path,
      version: _databaseVersion,
      onCreate: _createTables,
      onUpgrade: _upgradeTables,
    );
  }

  /// Create database tables
  static Future<void> _createTables(Database db, int version) async {
    // Snapshots table
    await db.execute('''
      CREATE TABLE $_snapshotsTable (
        id TEXT PRIMARY KEY,
        image_path TEXT NOT NULL,
        timestamp TEXT NOT NULL,
        total_detections INTEGER NOT NULL,
        average_confidence REAL NOT NULL,
        metadata TEXT
      )
    ''');
    
    // Detections table
    await db.execute('''
      CREATE TABLE $_detectionsTable (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        snapshot_id TEXT NOT NULL,
        label TEXT NOT NULL,
        confidence REAL NOT NULL,
        x REAL NOT NULL,
        y REAL NOT NULL,
        width REAL NOT NULL,
        height REAL NOT NULL,
        timestamp TEXT NOT NULL,
        FOREIGN KEY (snapshot_id) REFERENCES $_snapshotsTable (id)
      )
    ''');
    
    // Create indexes for better performance
    await db.execute('CREATE INDEX idx_snapshots_timestamp ON $_snapshotsTable (timestamp)');
    await db.execute('CREATE INDEX idx_detections_snapshot_id ON $_detectionsTable (snapshot_id)');
    await db.execute('CREATE INDEX idx_detections_label ON $_detectionsTable (label)');
  }

  /// Upgrade database tables
  static Future<void> _upgradeTables(Database db, int oldVersion, int newVersion) async {
    // Handle database migrations here
    if (oldVersion < 2) {
      // Add new columns or tables for version 2
    }
  }

  /// Save a snapshot with detections
  static Future<bool> saveSnapshot(Map<String, dynamic> snapshotData) async {
    if (_database == null) return false;
    
    try {
      await _database!.transaction((txn) async {
        // Insert snapshot
        await txn.insert(_snapshotsTable, {
          'id': snapshotData['id'],
          'image_path': snapshotData['imagePath'],
          'timestamp': snapshotData['timestamp'],
          'total_detections': snapshotData['totalDetections'],
          'average_confidence': snapshotData['averageConfidence'],
          'metadata': jsonEncode(snapshotData),
        });
        
        // Insert detections
        final detections = snapshotData['detections'] as List;
        for (final detection in detections) {
          final boundingBox = detection['boundingBox'];
          await txn.insert(_detectionsTable, {
            'snapshot_id': snapshotData['id'],
            'label': detection['label'],
            'confidence': detection['confidence'],
            'x': boundingBox['x'],
            'y': boundingBox['y'],
            'width': boundingBox['width'],
            'height': boundingBox['height'],
            'timestamp': detection['timestamp'],
          });
        }
      });
      
      print('Snapshot saved successfully: ${snapshotData['id']}');
      return true;
    } catch (e) {
      print('Error saving snapshot: $e');
      return false;
    }
  }

  /// Get all snapshots with pagination and date filtering
  static Future<List<Map<String, dynamic>>> getSnapshots({
    int limit = 20,
    int offset = 0,
    String? labelFilter,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    if (_database == null) return [];
    
    try {
      String query = '''
        SELECT s.*, COUNT(d.id) as detection_count
        FROM $_snapshotsTable s
        LEFT JOIN $_detectionsTable d ON s.id = d.snapshot_id
      ''';

      List<dynamic> args = [];
      List<String> whereConditions = [];

      // Add date range filters
      if (startDate != null) {
        whereConditions.add('s.timestamp >= ?');
        args.add(startDate.toIso8601String());
      }

      if (endDate != null) {
        whereConditions.add('s.timestamp <= ?');
        args.add(endDate.toIso8601String());
      }

      // Add label filter
      if (labelFilter != null && labelFilter.isNotEmpty) {
        whereConditions.add('d.label LIKE ?');
        args.add('%$labelFilter%');
      }

      // Add WHERE clause if conditions exist
      if (whereConditions.isNotEmpty) {
        query += ' WHERE ${whereConditions.join(' AND ')}';
      }
      
      query += '''
        GROUP BY s.id
        ORDER BY s.timestamp DESC
        LIMIT ? OFFSET ?
      ''';
      
      args.addAll([limit, offset]);
      
      final results = await _database!.rawQuery(query, args);
      
      // Load detections for each snapshot
      final snapshots = <Map<String, dynamic>>[];
      for (final snapshot in results) {
        final detections = await getDetectionsForSnapshot(snapshot['id'] as String);
        snapshots.add({
          ...snapshot,
          'detections': detections,
        });
      }
      
      return snapshots;
    } catch (e) {
      print('Error getting snapshots: $e');
      return [];
    }
  }

  /// Get detections for a specific snapshot
  static Future<List<Map<String, dynamic>>> getDetectionsForSnapshot(String snapshotId) async {
    if (_database == null) return [];
    
    try {
      final results = await _database!.query(
        _detectionsTable,
        where: 'snapshot_id = ?',
        whereArgs: [snapshotId],
        orderBy: 'confidence DESC',
      );
      
      return results.map((detection) => {
        'label': detection['label'],
        'confidence': detection['confidence'],
        'boundingBox': {
          'x': detection['x'],
          'y': detection['y'],
          'width': detection['width'],
          'height': detection['height'],
        },
        'timestamp': detection['timestamp'],
      }).toList();
    } catch (e) {
      print('Error getting detections for snapshot: $e');
      return [];
    }
  }

  /// Get snapshot by ID
  static Future<Map<String, dynamic>?> getSnapshot(String id) async {
    if (_database == null) return null;
    
    try {
      final results = await _database!.query(
        _snapshotsTable,
        where: 'id = ?',
        whereArgs: [id],
        limit: 1,
      );
      
      if (results.isEmpty) return null;
      
      final snapshot = results.first;
      final detections = await getDetectionsForSnapshot(id);
      
      return {
        ...snapshot,
        'detections': detections,
      };
    } catch (e) {
      print('Error getting snapshot: $e');
      return null;
    }
  }

  /// Delete snapshot and its detections
  static Future<bool> deleteSnapshot(String id) async {
    if (_database == null) return false;
    
    try {
      await _database!.transaction((txn) async {
        // Delete detections first
        await txn.delete(
          _detectionsTable,
          where: 'snapshot_id = ?',
          whereArgs: [id],
        );
        
        // Delete snapshot
        await txn.delete(
          _snapshotsTable,
          where: 'id = ?',
          whereArgs: [id],
        );
      });
      
      print('Snapshot deleted successfully: $id');
      return true;
    } catch (e) {
      print('Error deleting snapshot: $e');
      return false;
    }
  }

  /// Get detection statistics
  static Future<Map<String, dynamic>> getDetectionStats() async {
    if (_database == null) return {};
    
    try {
      // Total snapshots
      final snapshotCount = await _database!.rawQuery(
        'SELECT COUNT(*) as count FROM $_snapshotsTable'
      );
      
      // Total detections
      final detectionCount = await _database!.rawQuery(
        'SELECT COUNT(*) as count FROM $_detectionsTable'
      );
      
      // Most detected objects
      final topLabels = await _database!.rawQuery('''
        SELECT label, COUNT(*) as count
        FROM $_detectionsTable
        GROUP BY label
        ORDER BY count DESC
        LIMIT 10
      ''');
      
      // Average confidence
      final avgConfidence = await _database!.rawQuery(
        'SELECT AVG(confidence) as avg_confidence FROM $_detectionsTable'
      );
      
      return {
        'totalSnapshots': snapshotCount.first['count'],
        'totalDetections': detectionCount.first['count'],
        'topLabels': topLabels,
        'averageConfidence': avgConfidence.first['avg_confidence'] ?? 0.0,
      };
    } catch (e) {
      print('Error getting detection stats: $e');
      return {};
    }
  }

  /// Save app preferences
  static Future<bool> savePreference(String key, dynamic value) async {
    if (_prefs == null) return false;
    
    try {
      if (value is String) {
        return await _prefs!.setString(key, value);
      } else if (value is int) {
        return await _prefs!.setInt(key, value);
      } else if (value is double) {
        return await _prefs!.setDouble(key, value);
      } else if (value is bool) {
        return await _prefs!.setBool(key, value);
      } else if (value is List<String>) {
        return await _prefs!.setStringList(key, value);
      }
      return false;
    } catch (e) {
      print('Error saving preference: $e');
      return false;
    }
  }

  /// Get app preference
  static T? getPreference<T>(String key, [T? defaultValue]) {
    if (_prefs == null) return defaultValue;
    
    try {
      final value = _prefs!.get(key);
      return value as T? ?? defaultValue;
    } catch (e) {
      print('Error getting preference: $e');
      return defaultValue;
    }
  }

  /// Clear all data
  static Future<bool> clearAllData() async {
    if (_database == null) return false;
    
    try {
      await _database!.transaction((txn) async {
        await txn.delete(_detectionsTable);
        await txn.delete(_snapshotsTable);
      });
      
      print('All data cleared successfully');
      return true;
    } catch (e) {
      print('Error clearing data: $e');
      return false;
    }
  }

  /// Close database connection
  static Future<void> dispose() async {
    await _database?.close();
    _database = null;
  }
}
