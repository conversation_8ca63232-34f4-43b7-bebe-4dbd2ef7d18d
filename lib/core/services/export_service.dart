import 'dart:convert';
import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';

import 'storage_service.dart';

/// Service for exporting detection history and analytics
class ExportService {
  /// Export detection history as JSON
  static Future<bool> exportHistoryAsJson({
    DateTime? startDate,
    DateTime? endDate,
    int? limit,
  }) async {
    try {
      // Get snapshots from storage
      final snapshots = await StorageService.getSnapshots(
        startDate: startDate,
        endDate: endDate,
        limit: limit ?? 100,
      );

      if (snapshots.isEmpty) {
        print('No detection history to export');
        return false;
      }

      // Create export data structure
      final exportData = <String, dynamic>{
        'export_info': {
          'app_name': 'Visionary - AI Object Detection',
          'export_date': DateTime.now().toIso8601String(),
          'total_snapshots': snapshots.length,
          'date_range': {
            'start': startDate?.toIso8601String(),
            'end': endDate?.toIso8601String(),
          },
        },
        'snapshots': <Map<String, dynamic>>[],
      };

      // Process each snapshot
      for (final snapshot in snapshots) {
        final detections = await StorageService.getDetectionsForSnapshot(snapshot['id']);
        
        final snapshotData = {
          'id': snapshot['id'],
          'timestamp': snapshot['timestamp'],
          'image_path': snapshot['image_path'],
          'total_detections': snapshot['total_detections'],
          'average_confidence': snapshot['average_confidence'],
          'metadata': snapshot['metadata'],
          'detections': detections.map((detection) => {
            'label': detection['label'],
            'confidence': detection['confidence'],
            'bounding_box': detection['boundingBox'],
            'timestamp': detection['timestamp'],
          }).toList(),
        };
        
        (exportData['snapshots'] as List<Map<String, dynamic>>).add(snapshotData);
      }

      // Convert to JSON
      final jsonString = JsonEncoder.withIndent('  ').convert(exportData);

      // Save to file
      final directory = await getApplicationDocumentsDirectory();
      final fileName = 'visionary_export_${DateTime.now().millisecondsSinceEpoch}.json';
      final file = File('${directory.path}/$fileName');
      await file.writeAsString(jsonString);

      print('Exported ${snapshots.length} snapshots to $fileName');
      return true;
    } catch (e) {
      print('Error exporting history as JSON: $e');
      return false;
    }
  }

  /// Export detection history as CSV
  static Future<bool> exportHistoryAsCsv({
    DateTime? startDate,
    DateTime? endDate,
    int? limit,
  }) async {
    try {
      // Get snapshots from storage
      final snapshots = await StorageService.getSnapshots(
        startDate: startDate,
        endDate: endDate,
        limit: limit ?? 100,
      );

      if (snapshots.isEmpty) {
        print('No detection history to export');
        return false;
      }

      // Create CSV content
      final csvLines = <String>[];
      
      // CSV Header
      csvLines.add('Snapshot ID,Timestamp,Image Path,Total Detections,Average Confidence,Detection Label,Detection Confidence,Bounding Box X,Bounding Box Y,Bounding Box Width,Bounding Box Height');

      // Process each snapshot
      for (final snapshot in snapshots) {
        final detections = await StorageService.getDetectionsForSnapshot(snapshot['id']);
        
        if (detections.isEmpty) {
          // Add snapshot row even if no detections
          csvLines.add('${snapshot['id']},${snapshot['timestamp']},${snapshot['image_path']},${snapshot['total_detections']},${snapshot['average_confidence']},,,,,,');
        } else {
          // Add row for each detection
          for (final detection in detections) {
            final bbox = detection['boundingBox'];
            csvLines.add('${snapshot['id']},${snapshot['timestamp']},${snapshot['image_path']},${snapshot['total_detections']},${snapshot['average_confidence']},${detection['label']},${detection['confidence']},${bbox['x']},${bbox['y']},${bbox['width']},${bbox['height']}');
          }
        }
      }

      // Save to file
      final directory = await getApplicationDocumentsDirectory();
      final fileName = 'visionary_export_${DateTime.now().millisecondsSinceEpoch}.csv';
      final file = File('${directory.path}/$fileName');
      await file.writeAsString(csvLines.join('\n'));

      print('Exported ${snapshots.length} snapshots to CSV: $fileName');
      return true;
    } catch (e) {
      print('Error exporting history as CSV: $e');
      return false;
    }
  }

  /// Share detection history file
  static Future<bool> shareHistoryFile({
    String format = 'json',
    DateTime? startDate,
    DateTime? endDate,
    int? limit,
  }) async {
    try {
      bool exported = false;
      String fileName = '';

      if (format.toLowerCase() == 'csv') {
        exported = await exportHistoryAsCsv(
          startDate: startDate,
          endDate: endDate,
          limit: limit,
        );
        fileName = 'visionary_export_${DateTime.now().millisecondsSinceEpoch}.csv';
      } else {
        exported = await exportHistoryAsJson(
          startDate: startDate,
          endDate: endDate,
          limit: limit,
        );
        fileName = 'visionary_export_${DateTime.now().millisecondsSinceEpoch}.json';
      }

      if (!exported) {
        return false;
      }

      // Get file path
      final directory = await getApplicationDocumentsDirectory();
      final filePath = '${directory.path}/$fileName';

      // Share file
      await Share.shareXFiles(
        [XFile(filePath)],
        text: 'Visionary AI Detection History Export',
        subject: 'Detection History - ${DateTime.now().toString().split(' ')[0]}',
      );

      return true;
    } catch (e) {
      print('Error sharing history file: $e');
      return false;
    }
  }

  /// Generate analytics summary
  static Future<Map<String, dynamic>> generateAnalytics({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      // Get all snapshots for analytics
      final snapshots = await StorageService.getSnapshots(
        startDate: startDate,
        endDate: endDate,
      );

      if (snapshots.isEmpty) {
        return {
          'total_snapshots': 0,
          'total_detections': 0,
          'average_confidence': 0.0,
          'most_detected_objects': <String, int>{},
          'detection_trends': <String, int>{},
        };
      }

      int totalDetections = 0;
      double totalConfidence = 0.0;
      final Map<String, int> objectCounts = {};
      final Map<String, int> dailyCounts = {};

      // Process each snapshot
      for (final snapshot in snapshots) {
        final detections = await StorageService.getDetectionsForSnapshot(snapshot['id']);
        totalDetections += detections.length;

        // Track daily counts
        final date = DateTime.parse(snapshot['timestamp']).toString().split(' ')[0];
        dailyCounts[date] = (dailyCounts[date] ?? 0) + detections.length;

        // Process detections
        for (final detection in detections) {
          final label = detection['label'] as String;
          final confidence = detection['confidence'] as double;
          
          objectCounts[label] = (objectCounts[label] ?? 0) + 1;
          totalConfidence += confidence;
        }
      }

      // Sort most detected objects
      final sortedObjects = objectCounts.entries.toList()
        ..sort((a, b) => b.value.compareTo(a.value));

      return {
        'total_snapshots': snapshots.length,
        'total_detections': totalDetections,
        'average_confidence': totalDetections > 0 ? totalConfidence / totalDetections : 0.0,
        'most_detected_objects': Map.fromEntries(sortedObjects.take(10)),
        'detection_trends': dailyCounts,
        'date_range': {
          'start': startDate?.toIso8601String(),
          'end': endDate?.toIso8601String(),
        },
      };
    } catch (e) {
      print('Error generating analytics: $e');
      return {};
    }
  }

  /// Export analytics as JSON
  static Future<bool> exportAnalytics({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final analytics = await generateAnalytics(
        startDate: startDate,
        endDate: endDate,
      );

      if (analytics.isEmpty) {
        return false;
      }

      // Add export metadata
      analytics['export_info'] = {
        'app_name': 'Visionary - AI Object Detection',
        'export_type': 'analytics',
        'export_date': DateTime.now().toIso8601String(),
      };

      // Convert to JSON
      final jsonString = JsonEncoder.withIndent('  ').convert(analytics);

      // Save to file
      final directory = await getApplicationDocumentsDirectory();
      final fileName = 'visionary_analytics_${DateTime.now().millisecondsSinceEpoch}.json';
      final file = File('${directory.path}/$fileName');
      await file.writeAsString(jsonString);

      print('Exported analytics to $fileName');
      return true;
    } catch (e) {
      print('Error exporting analytics: $e');
      return false;
    }
  }
}
