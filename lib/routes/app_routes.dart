import 'package:flutter/material.dart';
import '../presentation/splash_screen/splash_screen.dart';
import '../presentation/permission_request_screen/permission_request_screen.dart';
import '../presentation/onboarding_flow/onboarding_flow.dart';
import '../presentation/camera_detection_screen/camera_detection_screen.dart';
import '../presentation/detection_detail_screen/detection_detail_screen.dart';
import '../presentation/detection_history_screen/detection_history_screen.dart';

class AppRoutes {
  // TODO: Add your routes here
  static const String initial = '/';
  static const String splashScreen = '/splash-screen';
  static const String permissionRequestScreen = '/permission-request-screen';
  static const String onboardingFlow = '/onboarding-flow';
  static const String cameraDetectionScreen = '/camera-detection-screen';
  static const String detectionDetailScreen = '/detection-detail-screen';
  static const String detectionHistoryScreen = '/detection-history-screen';

  static Map<String, WidgetBuilder> routes = {
    initial: (context) => const SplashScreen(),
    splashScreen: (context) => const SplashScreen(),
    permissionRequestScreen: (context) => const PermissionRequestScreen(),
    onboardingFlow: (context) => const OnboardingFlow(),
    cameraDetectionScreen: (context) => const CameraDetectionScreen(),
    detectionDetailScreen: (context) => const DetectionDetailScreen(),
    detectionHistoryScreen: (context) => const DetectionHistoryScreen(),
    // TODO: Add your other routes here
  };
}
