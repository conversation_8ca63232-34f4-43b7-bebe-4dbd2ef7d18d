import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

class EmptyStateWidget extends StatelessWidget {
  final VoidCallback onStartDetecting;

  const EmptyStateWidget({
    super.key,
    required this.onStartDetecting,
  });

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Center(
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 8.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Illustration Container
            Container(
              width: 60.w,
              height: 30.h,
              decoration: BoxDecoration(
                color: (isDark ? AppTheme.primaryDark : AppTheme.primaryLight)
                    .withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(24),
                border: Border.all(
                  color: (isDark ? AppTheme.primaryDark : AppTheme.primaryLight)
                      .withValues(alpha: 0.2),
                  width: 2,
                ),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Camera Icon
                  Container(
                    width: 20.w,
                    height: 20.w,
                    decoration: BoxDecoration(
                      color:
                          isDark ? AppTheme.primaryDark : AppTheme.primaryLight,
                      shape: BoxShape.circle,
                    ),
                    child: CustomIconWidget(
                      iconName: 'camera_alt',
                      color: isDark ? Colors.black : Colors.white,
                      size: 32,
                    ),
                  ),
                  SizedBox(height: 2.h),

                  // Detection Indicators
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      _buildDetectionDot(
                          isDark, AppTheme.successLight, AppTheme.successDark),
                      SizedBox(width: 2.w),
                      _buildDetectionDot(
                          isDark, AppTheme.warningLight, AppTheme.warningDark),
                      SizedBox(width: 2.w),
                      _buildDetectionDot(
                          isDark, AppTheme.errorLight, AppTheme.errorDark),
                    ],
                  ),
                ],
              ),
            ),

            SizedBox(height: 4.h),

            // Title
            Text(
              'No Detections Yet',
              style: AppTheme.lightTheme.textTheme.headlineSmall?.copyWith(
                color: isDark
                    ? AppTheme.textPrimaryDark
                    : AppTheme.textPrimaryLight,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),

            SizedBox(height: 2.h),

            // Description
            Text(
              'Start detecting objects with your camera to see your detection history here. All your captured detections will be saved and organized for easy access.',
              style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                color: isDark
                    ? AppTheme.textSecondaryDark
                    : AppTheme.textSecondaryLight,
                height: 1.5,
              ),
              textAlign: TextAlign.center,
            ),

            SizedBox(height: 4.h),

            // Action Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: onStartDetecting,
                style: ElevatedButton.styleFrom(
                  backgroundColor:
                      isDark ? AppTheme.primaryDark : AppTheme.primaryLight,
                  foregroundColor: isDark ? Colors.black : Colors.white,
                  padding: EdgeInsets.symmetric(vertical: 2.h),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                  elevation: 2,
                ),
                icon: CustomIconWidget(
                  iconName: 'camera_alt',
                  color: isDark ? Colors.black : Colors.white,
                  size: 24,
                ),
                label: Text(
                  'Start Detecting Objects',
                  style: AppTheme.lightTheme.textTheme.labelLarge?.copyWith(
                    color: isDark ? Colors.black : Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),

            SizedBox(height: 2.h),

            // Secondary Action
            TextButton.icon(
              onPressed: () {
                // Show tips or tutorial
                _showDetectionTips(context, isDark);
              },
              icon: CustomIconWidget(
                iconName: 'lightbulb_outline',
                color: isDark
                    ? AppTheme.textSecondaryDark
                    : AppTheme.textSecondaryLight,
                size: 20,
              ),
              label: Text(
                'Detection Tips',
                style: AppTheme.lightTheme.textTheme.labelMedium?.copyWith(
                  color: isDark
                      ? AppTheme.textSecondaryDark
                      : AppTheme.textSecondaryLight,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetectionDot(bool isDark, Color lightColor, Color darkColor) {
    return Container(
      width: 3.w,
      height: 3.w,
      decoration: BoxDecoration(
        color: isDark ? darkColor : lightColor,
        shape: BoxShape.circle,
      ),
    );
  }

  void _showDetectionTips(BuildContext context, bool isDark) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: isDark ? AppTheme.surfaceDark : AppTheme.surfaceLight,
        title: Row(
          children: [
            CustomIconWidget(
              iconName: 'lightbulb',
              color: isDark ? AppTheme.warningDark : AppTheme.warningLight,
              size: 24,
            ),
            SizedBox(width: 2.w),
            Text(
              'Detection Tips',
              style: AppTheme.lightTheme.textTheme.titleLarge?.copyWith(
                color: isDark
                    ? AppTheme.textPrimaryDark
                    : AppTheme.textPrimaryLight,
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildTipItem(
              isDark,
              'Good Lighting',
              'Ensure adequate lighting for better detection accuracy',
              'wb_sunny',
            ),
            SizedBox(height: 1.5.h),
            _buildTipItem(
              isDark,
              'Steady Camera',
              'Hold your device steady for clearer object recognition',
              'camera_enhance',
            ),
            SizedBox(height: 1.5.h),
            _buildTipItem(
              isDark,
              'Clear View',
              'Position objects clearly in the camera frame',
              'center_focus_strong',
            ),
            SizedBox(height: 1.5.h),
            _buildTipItem(
              isDark,
              'Multiple Angles',
              'Try different angles for better detection results',
              '360',
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Got it!',
              style: AppTheme.lightTheme.textTheme.labelLarge?.copyWith(
                color: isDark ? AppTheme.primaryDark : AppTheme.primaryLight,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTipItem(
      bool isDark, String title, String description, String iconName) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          width: 8.w,
          height: 8.w,
          decoration: BoxDecoration(
            color: (isDark ? AppTheme.primaryDark : AppTheme.primaryLight)
                .withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: CustomIconWidget(
            iconName: iconName,
            color: isDark ? AppTheme.primaryDark : AppTheme.primaryLight,
            size: 20,
          ),
        ),
        SizedBox(width: 3.w),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: AppTheme.lightTheme.textTheme.titleSmall?.copyWith(
                  color: isDark
                      ? AppTheme.textPrimaryDark
                      : AppTheme.textPrimaryLight,
                  fontWeight: FontWeight.w600,
                ),
              ),
              SizedBox(height: 0.5.h),
              Text(
                description,
                style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                  color: isDark
                      ? AppTheme.textSecondaryDark
                      : AppTheme.textSecondaryLight,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
