import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

class SearchFilterWidget extends StatefulWidget {
  final String selectedFilter;
  final DateTimeRange? selectedDateRange;
  final double confidenceThreshold;
  final Function(String filter, DateTimeRange? dateRange, double confidence)
      onFilterChanged;

  const SearchFilterWidget({
    super.key,
    required this.selectedFilter,
    required this.selectedDateRange,
    required this.confidenceThreshold,
    required this.onFilterChanged,
  });

  @override
  State<SearchFilterWidget> createState() => _SearchFilterWidgetState();
}

class _SearchFilterWidgetState extends State<SearchFilterWidget> {
  late String _selectedFilter;
  late DateTimeRange? _selectedDateRange;
  late double _confidenceThreshold;

  final List<String> _filterOptions = [
    'All',
    'People',
    'Animals',
    'Vehicles',
    'Electronics',
    'Furniture',
    'Objects',
    'Structures',
  ];

  @override
  void initState() {
    super.initState();
    _selectedFilter = widget.selectedFilter;
    _selectedDateRange = widget.selectedDateRange;
    _confidenceThreshold = widget.confidenceThreshold;
  }

  Future<void> _selectDateRange() async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now(),
      initialDateRange: _selectedDateRange,
      builder: (context, child) {
        final isDark = Theme.of(context).brightness == Brightness.dark;
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: isDark ? AppTheme.primaryDark : AppTheme.primaryLight,
              onPrimary: isDark ? Colors.black : Colors.white,
              surface: isDark ? AppTheme.surfaceDark : AppTheme.surfaceLight,
              onSurface:
                  isDark ? AppTheme.textPrimaryDark : AppTheme.textPrimaryLight,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      setState(() {
        _selectedDateRange = picked;
      });
    }
  }

  void _clearDateRange() {
    setState(() {
      _selectedDateRange = null;
    });
  }

  void _applyFilters() {
    widget.onFilterChanged(
        _selectedFilter, _selectedDateRange, _confidenceThreshold);
    Navigator.pop(context);
  }

  void _resetFilters() {
    setState(() {
      _selectedFilter = 'All';
      _selectedDateRange = null;
      _confidenceThreshold = 0.0;
    });
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Container(
      height: 85.h,
      decoration: BoxDecoration(
        color: isDark ? AppTheme.surfaceDark : AppTheme.surfaceLight,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(24)),
      ),
      child: Column(
        children: [
          // Handle Bar
          Container(
            width: 12.w,
            height: 0.5.h,
            margin: EdgeInsets.symmetric(vertical: 2.h),
            decoration: BoxDecoration(
              color: isDark ? AppTheme.borderDark : AppTheme.borderLight,
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Header
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 6.w),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Filter Detections',
                  style: AppTheme.lightTheme.textTheme.headlineSmall?.copyWith(
                    color: isDark
                        ? AppTheme.textPrimaryDark
                        : AppTheme.textPrimaryLight,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                TextButton(
                  onPressed: _resetFilters,
                  child: Text(
                    'Reset',
                    style: AppTheme.lightTheme.textTheme.labelLarge?.copyWith(
                      color:
                          isDark ? AppTheme.primaryDark : AppTheme.primaryLight,
                    ),
                  ),
                ),
              ],
            ),
          ),

          Expanded(
            child: SingleChildScrollView(
              padding: EdgeInsets.symmetric(horizontal: 6.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(height: 2.h),

                  // Category Filter
                  Text(
                    'Object Category',
                    style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                      color: isDark
                          ? AppTheme.textPrimaryDark
                          : AppTheme.textPrimaryLight,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  SizedBox(height: 1.h),
                  Wrap(
                    spacing: 2.w,
                    runSpacing: 1.h,
                    children: _filterOptions.map((filter) {
                      final isSelected = _selectedFilter == filter;
                      return GestureDetector(
                        onTap: () {
                          setState(() {
                            _selectedFilter = filter;
                          });
                        },
                        child: Container(
                          padding: EdgeInsets.symmetric(
                              horizontal: 4.w, vertical: 1.h),
                          decoration: BoxDecoration(
                            color: isSelected
                                ? (isDark
                                    ? AppTheme.primaryDark
                                    : AppTheme.primaryLight)
                                : Colors.transparent,
                            borderRadius: BorderRadius.circular(20),
                            border: Border.all(
                              color: isSelected
                                  ? (isDark
                                      ? AppTheme.primaryDark
                                      : AppTheme.primaryLight)
                                  : (isDark
                                      ? AppTheme.borderDark
                                      : AppTheme.borderLight),
                            ),
                          ),
                          child: Text(
                            filter,
                            style: AppTheme.lightTheme.textTheme.labelMedium
                                ?.copyWith(
                              color: isSelected
                                  ? (isDark ? Colors.black : Colors.white)
                                  : (isDark
                                      ? AppTheme.textPrimaryDark
                                      : AppTheme.textPrimaryLight),
                              fontWeight: isSelected
                                  ? FontWeight.w600
                                  : FontWeight.w400,
                            ),
                          ),
                        ),
                      );
                    }).toList(),
                  ),

                  SizedBox(height: 3.h),

                  // Date Range Filter
                  Text(
                    'Date Range',
                    style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                      color: isDark
                          ? AppTheme.textPrimaryDark
                          : AppTheme.textPrimaryLight,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  SizedBox(height: 1.h),
                  Container(
                    width: double.infinity,
                    padding: EdgeInsets.all(4.w),
                    decoration: BoxDecoration(
                      color: isDark
                          ? AppTheme.backgroundDark
                          : AppTheme.backgroundLight,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color:
                            isDark ? AppTheme.borderDark : AppTheme.borderLight,
                      ),
                    ),
                    child: Column(
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              _selectedDateRange != null
                                  ? '${_selectedDateRange!.start.day}/${_selectedDateRange!.start.month}/${_selectedDateRange!.start.year} - ${_selectedDateRange!.end.day}/${_selectedDateRange!.end.month}/${_selectedDateRange!.end.year}'
                                  : 'Select date range',
                              style: AppTheme.lightTheme.textTheme.bodyMedium
                                  ?.copyWith(
                                color: _selectedDateRange != null
                                    ? (isDark
                                        ? AppTheme.textPrimaryDark
                                        : AppTheme.textPrimaryLight)
                                    : (isDark
                                        ? AppTheme.textSecondaryDark
                                        : AppTheme.textSecondaryLight),
                              ),
                            ),
                            if (_selectedDateRange != null)
                              GestureDetector(
                                onTap: _clearDateRange,
                                child: CustomIconWidget(
                                  iconName: 'clear',
                                  color: isDark
                                      ? AppTheme.textSecondaryDark
                                      : AppTheme.textSecondaryLight,
                                  size: 20,
                                ),
                              ),
                          ],
                        ),
                        SizedBox(height: 2.h),
                        SizedBox(
                          width: double.infinity,
                          child: OutlinedButton.icon(
                            onPressed: _selectDateRange,
                            icon: CustomIconWidget(
                              iconName: 'calendar_today',
                              color: isDark
                                  ? AppTheme.primaryDark
                                  : AppTheme.primaryLight,
                              size: 18,
                            ),
                            label: Text(
                              _selectedDateRange != null
                                  ? 'Change Range'
                                  : 'Select Range',
                              style: AppTheme.lightTheme.textTheme.labelLarge
                                  ?.copyWith(
                                color: isDark
                                    ? AppTheme.primaryDark
                                    : AppTheme.primaryLight,
                              ),
                            ),
                            style: OutlinedButton.styleFrom(
                              side: BorderSide(
                                color: isDark
                                    ? AppTheme.primaryDark
                                    : AppTheme.primaryLight,
                              ),
                              padding: EdgeInsets.symmetric(vertical: 1.5.h),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                  SizedBox(height: 3.h),

                  // Confidence Threshold
                  Text(
                    'Minimum Confidence',
                    style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                      color: isDark
                          ? AppTheme.textPrimaryDark
                          : AppTheme.textPrimaryLight,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  SizedBox(height: 1.h),
                  Container(
                    width: double.infinity,
                    padding: EdgeInsets.all(4.w),
                    decoration: BoxDecoration(
                      color: isDark
                          ? AppTheme.backgroundDark
                          : AppTheme.backgroundLight,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color:
                            isDark ? AppTheme.borderDark : AppTheme.borderLight,
                      ),
                    ),
                    child: Column(
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              'Confidence: ${(_confidenceThreshold * 100).toInt()}%',
                              style: AppTheme.lightTheme.textTheme.bodyMedium
                                  ?.copyWith(
                                color: isDark
                                    ? AppTheme.textPrimaryDark
                                    : AppTheme.textPrimaryLight,
                              ),
                            ),
                            Container(
                              padding: EdgeInsets.symmetric(
                                  horizontal: 3.w, vertical: 0.5.h),
                              decoration: BoxDecoration(
                                color: _confidenceThreshold >= 0.8
                                    ? (isDark
                                        ? AppTheme.successDark
                                        : AppTheme.successLight)
                                    : _confidenceThreshold >= 0.6
                                        ? (isDark
                                            ? AppTheme.warningDark
                                            : AppTheme.warningLight)
                                        : (isDark
                                            ? AppTheme.errorDark
                                            : AppTheme.errorLight),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Text(
                                _confidenceThreshold >= 0.8
                                    ? 'High'
                                    : _confidenceThreshold >= 0.6
                                        ? 'Medium'
                                        : 'Low',
                                style: AppTheme.lightTheme.textTheme.labelSmall
                                    ?.copyWith(
                                  color: Colors.white,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: 2.h),
                        SliderTheme(
                          data: SliderTheme.of(context).copyWith(
                            activeTrackColor: isDark
                                ? AppTheme.primaryDark
                                : AppTheme.primaryLight,
                            inactiveTrackColor: (isDark
                                    ? AppTheme.primaryDark
                                    : AppTheme.primaryLight)
                                .withValues(alpha: 0.3),
                            thumbColor: isDark
                                ? AppTheme.primaryDark
                                : AppTheme.primaryLight,
                            overlayColor: (isDark
                                    ? AppTheme.primaryDark
                                    : AppTheme.primaryLight)
                                .withValues(alpha: 0.2),
                            trackHeight: 4,
                          ),
                          child: Slider(
                            value: _confidenceThreshold,
                            min: 0.0,
                            max: 1.0,
                            divisions: 10,
                            onChanged: (value) {
                              setState(() {
                                _confidenceThreshold = value;
                              });
                            },
                          ),
                        ),
                      ],
                    ),
                  ),

                  SizedBox(height: 4.h),
                ],
              ),
            ),
          ),

          // Action Buttons
          Container(
            padding: EdgeInsets.all(6.w),
            decoration: BoxDecoration(
              color: isDark ? AppTheme.surfaceDark : AppTheme.surfaceLight,
              border: Border(
                top: BorderSide(
                  color: isDark ? AppTheme.borderDark : AppTheme.borderLight,
                ),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => Navigator.pop(context),
                    style: OutlinedButton.styleFrom(
                      padding: EdgeInsets.symmetric(vertical: 1.5.h),
                      side: BorderSide(
                        color:
                            isDark ? AppTheme.borderDark : AppTheme.borderLight,
                      ),
                    ),
                    child: Text(
                      'Cancel',
                      style: AppTheme.lightTheme.textTheme.labelLarge?.copyWith(
                        color: isDark
                            ? AppTheme.textPrimaryDark
                            : AppTheme.textPrimaryLight,
                      ),
                    ),
                  ),
                ),
                SizedBox(width: 4.w),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _applyFilters,
                    style: ElevatedButton.styleFrom(
                      backgroundColor:
                          isDark ? AppTheme.primaryDark : AppTheme.primaryLight,
                      foregroundColor: isDark ? Colors.black : Colors.white,
                      padding: EdgeInsets.symmetric(vertical: 1.5.h),
                    ),
                    child: Text(
                      'Apply Filters',
                      style: AppTheme.lightTheme.textTheme.labelLarge?.copyWith(
                        color: isDark ? Colors.black : Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
