import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

class DetectionHistoryCardWidget extends StatelessWidget {
  final Map<String, dynamic> detection;
  final bool isMultiSelectMode;
  final bool isSelected;
  final VoidCallback onTap;
  final VoidCallback onLongPress;
  final VoidCallback onShare;
  final VoidCallback onDelete;

  const DetectionHistoryCardWidget({
    super.key,
    required this.detection,
    required this.isMultiSelectMode,
    required this.isSelected,
    required this.onTap,
    required this.onLongPress,
    required this.onShare,
    required this.onDelete,
  });

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d ago';
    } else {
      return '${timestamp.day}/${timestamp.month}/${timestamp.year}';
    }
  }

  Color _getConfidenceColor(double confidence, bool isDark) {
    if (confidence >= 0.8) {
      return isDark ? AppTheme.successDark : AppTheme.successLight;
    } else if (confidence >= 0.6) {
      return isDark ? AppTheme.warningDark : AppTheme.warningLight;
    } else {
      return isDark ? AppTheme.errorDark : AppTheme.errorLight;
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final timestamp = detection['timestamp'] as DateTime;
    final detectedObjects = detection['detectedObjects'] as List;
    final imageUrl = detection['imageUrl'] as String;
    final location = detection['location'] as String;
    final totalObjects = detection['totalObjects'] as int;

    return Container(
      margin: EdgeInsets.only(bottom: 2.h),
      decoration: BoxDecoration(
        color: isSelected
            ? (isDark
                ? AppTheme.primaryDark.withValues(alpha: 0.1)
                : AppTheme.primaryLight.withValues(alpha: 0.1))
            : (isDark ? AppTheme.surfaceDark : AppTheme.surfaceLight),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: isSelected
              ? (isDark ? AppTheme.primaryDark : AppTheme.primaryLight)
              : (isDark ? AppTheme.borderDark : AppTheme.borderLight),
          width: isSelected ? 2 : 1,
        ),
        boxShadow: [
          BoxShadow(
            color: isDark ? AppTheme.shadowDark : AppTheme.shadowLight,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          onLongPress: onLongPress,
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: EdgeInsets.all(4.w),
            child: Row(
              children: [
                // Selection Checkbox
                if (isMultiSelectMode) ...[
                  Container(
                    width: 6.w,
                    height: 6.w,
                    margin: EdgeInsets.only(right: 3.w),
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: isSelected
                          ? (isDark
                              ? AppTheme.primaryDark
                              : AppTheme.primaryLight)
                          : Colors.transparent,
                      border: Border.all(
                        color: isSelected
                            ? (isDark
                                ? AppTheme.primaryDark
                                : AppTheme.primaryLight)
                            : (isDark
                                ? AppTheme.borderDark
                                : AppTheme.borderLight),
                        width: 2,
                      ),
                    ),
                    child: isSelected
                        ? CustomIconWidget(
                            iconName: 'check',
                            color: isDark ? Colors.black : Colors.white,
                            size: 16,
                          )
                        : null,
                  ),
                ],

                // Thumbnail Image
                Container(
                  width: 20.w,
                  height: 20.w,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color:
                          isDark ? AppTheme.borderDark : AppTheme.borderLight,
                    ),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(11),
                    child: CustomImageWidget(
                      imageUrl: imageUrl,
                      width: 20.w,
                      height: 20.w,
                      fit: BoxFit.cover,
                    ),
                  ),
                ),

                SizedBox(width: 4.w),

                // Detection Details
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Header Row
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            child: Text(
                              '$totalObjects Object${totalObjects > 1 ? 's' : ''} Detected',
                              style: AppTheme.lightTheme.textTheme.titleMedium
                                  ?.copyWith(
                                color: isDark
                                    ? AppTheme.textPrimaryDark
                                    : AppTheme.textPrimaryLight,
                                fontWeight: FontWeight.w600,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          if (!isMultiSelectMode) ...[
                            PopupMenuButton<String>(
                              onSelected: (value) {
                                if (value == 'share') {
                                  onShare();
                                } else if (value == 'delete') {
                                  onDelete();
                                }
                              },
                              itemBuilder: (context) => [
                                PopupMenuItem(
                                  value: 'share',
                                  child: Row(
                                    children: [
                                      CustomIconWidget(
                                        iconName: 'share',
                                        color: isDark
                                            ? AppTheme.textPrimaryDark
                                            : AppTheme.textPrimaryLight,
                                        size: 18,
                                      ),
                                      SizedBox(width: 2.w),
                                      Text(
                                        'Share',
                                        style: AppTheme
                                            .lightTheme.textTheme.bodyMedium
                                            ?.copyWith(
                                          color: isDark
                                              ? AppTheme.textPrimaryDark
                                              : AppTheme.textPrimaryLight,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                PopupMenuItem(
                                  value: 'delete',
                                  child: Row(
                                    children: [
                                      CustomIconWidget(
                                        iconName: 'delete',
                                        color: isDark
                                            ? AppTheme.errorDark
                                            : AppTheme.errorLight,
                                        size: 18,
                                      ),
                                      SizedBox(width: 2.w),
                                      Text(
                                        'Delete',
                                        style: AppTheme
                                            .lightTheme.textTheme.bodyMedium
                                            ?.copyWith(
                                          color: isDark
                                              ? AppTheme.errorDark
                                              : AppTheme.errorLight,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                              child: CustomIconWidget(
                                iconName: 'more_vert',
                                color: isDark
                                    ? AppTheme.textSecondaryDark
                                    : AppTheme.textSecondaryLight,
                                size: 20,
                              ),
                            ),
                          ],
                        ],
                      ),

                      SizedBox(height: 1.h),

                      // Detected Objects
                      Wrap(
                        spacing: 2.w,
                        runSpacing: 0.5.h,
                        children: detectedObjects.take(3).map((obj) {
                          final objMap = obj as Map<String, dynamic>;
                          final name = objMap['name'] as String;
                          final confidence = objMap['confidence'] as double;

                          return Container(
                            padding: EdgeInsets.symmetric(
                                horizontal: 2.w, vertical: 0.5.h),
                            decoration: BoxDecoration(
                              color: _getConfidenceColor(confidence, isDark)
                                  .withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: _getConfidenceColor(confidence, isDark)
                                    .withValues(alpha: 0.3),
                              ),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text(
                                  name,
                                  style: AppTheme
                                      .lightTheme.textTheme.labelSmall
                                      ?.copyWith(
                                    color:
                                        _getConfidenceColor(confidence, isDark),
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                SizedBox(width: 1.w),
                                Text(
                                  '${(confidence * 100).toInt()}%',
                                  style: AppTheme
                                      .lightTheme.textTheme.labelSmall
                                      ?.copyWith(
                                    color:
                                        _getConfidenceColor(confidence, isDark)
                                            .withValues(alpha: 0.8),
                                    fontWeight: FontWeight.w400,
                                  ),
                                ),
                              ],
                            ),
                          );
                        }).toList(),
                      ),

                      if (detectedObjects.length > 3) ...[
                        SizedBox(height: 0.5.h),
                        Text(
                          '+${detectedObjects.length - 3} more',
                          style: AppTheme.lightTheme.textTheme.labelSmall
                              ?.copyWith(
                            color: isDark
                                ? AppTheme.textSecondaryDark
                                : AppTheme.textSecondaryLight,
                            fontStyle: FontStyle.italic,
                          ),
                        ),
                      ],

                      SizedBox(height: 1.h),

                      // Location and Timestamp
                      Row(
                        children: [
                          CustomIconWidget(
                            iconName: 'location_on',
                            color: isDark
                                ? AppTheme.textSecondaryDark
                                : AppTheme.textSecondaryLight,
                            size: 14,
                          ),
                          SizedBox(width: 1.w),
                          Expanded(
                            child: Text(
                              location,
                              style: AppTheme.lightTheme.textTheme.bodySmall
                                  ?.copyWith(
                                color: isDark
                                    ? AppTheme.textSecondaryDark
                                    : AppTheme.textSecondaryLight,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          SizedBox(width: 2.w),
                          CustomIconWidget(
                            iconName: 'access_time',
                            color: isDark
                                ? AppTheme.textSecondaryDark
                                : AppTheme.textSecondaryLight,
                            size: 14,
                          ),
                          SizedBox(width: 1.w),
                          Text(
                            _formatTimestamp(timestamp),
                            style: AppTheme.lightTheme.textTheme.bodySmall
                                ?.copyWith(
                              color: isDark
                                  ? AppTheme.textSecondaryDark
                                  : AppTheme.textSecondaryLight,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
