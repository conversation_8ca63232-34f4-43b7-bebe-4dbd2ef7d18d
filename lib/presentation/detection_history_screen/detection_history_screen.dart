import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../core/app_export.dart';
import '../../core/services/storage_service.dart';
import '../../core/services/export_service.dart';
import './widgets/detection_history_card_widget.dart';
import './widgets/empty_state_widget.dart';
import './widgets/search_filter_widget.dart';

class DetectionHistoryScreen extends StatefulWidget {
  const DetectionHistoryScreen({super.key});

  @override
  State<DetectionHistoryScreen> createState() => _DetectionHistoryScreenState();
}

class _DetectionHistoryScreenState extends State<DetectionHistoryScreen> {
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  List<Map<String, dynamic>> _detectionHistory = [];
  List<Map<String, dynamic>> _filteredHistory = [];
  bool _isLoading = false;
  bool _isSearching = false;
  bool _isMultiSelectMode = false;
  final Set<int> _selectedItems = {};
  String _selectedFilter = 'All';
  DateTimeRange? _selectedDateRange;
  double _confidenceThreshold = 0.0;

  @override
  void initState() {
    super.initState();
    _loadDetectionHistory();
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  /// Load detection history from local storage
  Future<void> _loadDetectionHistory() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Load snapshots from storage
      final snapshots = await StorageService.getSnapshots(limit: 50);

      // Convert storage format to UI format
      final List<Map<String, dynamic>> historyData = [];

      for (final snapshot in snapshots) {
        // Get detections for this snapshot
        final detections = await StorageService.getDetectionsForSnapshot(snapshot['id']);

        // Convert to UI format
        final historyItem = {
          "id": snapshot['id'],
          "timestamp": DateTime.parse(snapshot['timestamp']),
          "imageUrl": snapshot['image_path'], // Local file path
          "detectedObjects": detections.map((detection) => {
            "name": detection['label'],
            "confidence": detection['confidence'],
          }).toList(),
          "location": _extractLocationFromMetadata(snapshot['metadata']),
          "totalObjects": snapshot['total_detections'],
          "averageConfidence": snapshot['average_confidence'],
          "imagePath": snapshot['image_path'], // For local file access
        };

        historyData.add(historyItem);
      }

      setState(() {
        _detectionHistory = historyData;
        _filteredHistory = List.from(_detectionHistory);
        _isLoading = false;
      });

      print('Loaded ${historyData.length} detection history items from storage');
    } catch (e) {
      print('Error loading detection history: $e');

      // Fallback to empty list on error
      setState(() {
        _detectionHistory = [];
        _filteredHistory = [];
        _isLoading = false;
      });
    }
  }

  /// Extract location from metadata JSON
  String _extractLocationFromMetadata(String? metadata) {
    if (metadata == null || metadata.isEmpty) {
      return 'Unknown Location';
    }

    try {
      // Parse metadata JSON and extract location
      // For now, return a default location
      return 'Camera Detection';
    } catch (e) {
      return 'Unknown Location';
    }
  }

  void _onSearchChanged() {
    final query = _searchController.text.toLowerCase();
    setState(() {
      _isSearching = query.isNotEmpty;
      _filteredHistory = _detectionHistory.where((detection) {
        final objects = (detection['detectedObjects'] as List)
            .map((obj) => (obj as Map<String, dynamic>)['name'] as String)
            .join(' ')
            .toLowerCase();
        final location = (detection['location'] as String).toLowerCase();
        return objects.contains(query) || location.contains(query);
      }).toList();
    });
  }

  void _applyFilters() {
    setState(() {
      _filteredHistory = _detectionHistory.where((detection) {
        // Date range filter
        if (_selectedDateRange != null) {
          final detectionDate = detection['timestamp'] as DateTime;
          if (detectionDate.isBefore(_selectedDateRange!.start) ||
              detectionDate.isAfter(_selectedDateRange!.end)) {
            return false;
          }
        }

        // Confidence threshold filter
        final objects = detection['detectedObjects'] as List;
        final hasHighConfidence = objects.any((obj) {
          final confidence =
              (obj as Map<String, dynamic>)['confidence'] as double;
          return confidence >= _confidenceThreshold;
        });
        if (!hasHighConfidence) return false;

        // Category filter
        if (_selectedFilter != 'All') {
          final hasCategory = objects.any((obj) {
            final name = (obj as Map<String, dynamic>)['name'] as String;
            return _getCategoryForObject(name) == _selectedFilter;
          });
          if (!hasCategory) return false;
        }

        return true;
      }).toList();
    });
  }

  String _getCategoryForObject(String objectName) {
    const categories = {
      'Person': 'People',
      'Cat': 'Animals',
      'Dog': 'Animals',
      'Car': 'Vehicles',
      'Bicycle': 'Vehicles',
      'Phone': 'Electronics',
      'Laptop': 'Electronics',
      'Cup': 'Objects',
      'Ball': 'Objects',
      'Sofa': 'Furniture',
      'Traffic Light': 'Objects',
      'Building': 'Structures',
    };
    return categories[objectName] ?? 'Objects';
  }

  void _showFilterBottomSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => SearchFilterWidget(
        selectedFilter: _selectedFilter,
        selectedDateRange: _selectedDateRange,
        confidenceThreshold: _confidenceThreshold,
        onFilterChanged: (filter, dateRange, confidence) {
          setState(() {
            _selectedFilter = filter;
            _selectedDateRange = dateRange;
            _confidenceThreshold = confidence;
          });
          _applyFilters();
        },
      ),
    );
  }

  void _toggleMultiSelectMode() {
    setState(() {
      _isMultiSelectMode = !_isMultiSelectMode;
      if (!_isMultiSelectMode) {
        _selectedItems.clear();
      }
    });
  }

  void _toggleItemSelection(int id) {
    setState(() {
      if (_selectedItems.contains(id)) {
        _selectedItems.remove(id);
      } else {
        _selectedItems.add(id);
      }
    });
  }

  void _deleteSelectedItems() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Delete Selected Items',
          style: AppTheme.lightTheme.textTheme.titleLarge,
        ),
        content: Text(
          'Are you sure you want to delete ${_selectedItems.length} selected detection${_selectedItems.length > 1 ? 's' : ''}?',
          style: AppTheme.lightTheme.textTheme.bodyMedium,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              setState(() {
                _detectionHistory.removeWhere(
                    (detection) => _selectedItems.contains(detection['id']));
                _filteredHistory.removeWhere(
                    (detection) => _selectedItems.contains(detection['id']));
                _selectedItems.clear();
                _isMultiSelectMode = false;
              });
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Selected items deleted')),
              );
            },
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _shareSelectedItems() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
            'Sharing ${_selectedItems.length} detection${_selectedItems.length > 1 ? 's' : ''}'),
      ),
    );
  }

  /// Export detection history
  Future<void> _exportHistory() async {
    try {
      // Show export options dialog
      final format = await _showExportDialog();
      if (format == null) return;

      // Show loading
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Exporting detection history...'),
          duration: Duration(seconds: 2),
        ),
      );

      // Export based on selected format
      bool success = false;
      if (format == 'share_json') {
        success = await ExportService.shareHistoryFile(format: 'json');
      } else if (format == 'share_csv') {
        success = await ExportService.shareHistoryFile(format: 'csv');
      } else if (format == 'analytics') {
        success = await ExportService.exportAnalytics();
      }

      // Show result
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            success
              ? 'Export completed successfully'
              : 'Export failed. Please try again.',
          ),
          backgroundColor: success ? Colors.green : Colors.red,
          duration: const Duration(seconds: 3),
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Export error: $e'),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  /// Show export options dialog
  Future<String?> _showExportDialog() async {
    return showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Export Detection History'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.share),
              title: const Text('Share as JSON'),
              subtitle: const Text('Complete data with metadata'),
              onTap: () => Navigator.pop(context, 'share_json'),
            ),
            ListTile(
              leading: const Icon(Icons.table_chart),
              title: const Text('Share as CSV'),
              subtitle: const Text('Spreadsheet format'),
              onTap: () => Navigator.pop(context, 'share_csv'),
            ),
            ListTile(
              leading: const Icon(Icons.analytics),
              title: const Text('Export Analytics'),
              subtitle: const Text('Summary and statistics'),
              onTap: () => Navigator.pop(context, 'analytics'),
            ),
            ListTile(
              leading: const Icon(Icons.insights),
              title: const Text('View Analytics'),
              subtitle: const Text('Display statistics'),
              onTap: () {
                Navigator.pop(context);
                _showAnalytics();
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  /// Show analytics dialog
  Future<void> _showAnalytics() async {
    try {
      // Show loading
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(
          child: CircularProgressIndicator(),
        ),
      );

      // Generate analytics
      final analytics = await ExportService.generateAnalytics();

      // Close loading dialog
      Navigator.pop(context);

      if (analytics.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('No data available for analytics'),
            duration: Duration(seconds: 2),
          ),
        );
        return;
      }

      // Show analytics dialog
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Detection Analytics'),
          content: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildAnalyticsItem('Total Snapshots', '${analytics['total_snapshots']}'),
                _buildAnalyticsItem('Total Detections', '${analytics['total_detections']}'),
                _buildAnalyticsItem('Average Confidence', '${(analytics['average_confidence'] * 100).toStringAsFixed(1)}%'),
                const SizedBox(height: 16),
                const Text('Most Detected Objects:', style: TextStyle(fontWeight: FontWeight.bold)),
                const SizedBox(height: 8),
                ...((analytics['most_detected_objects'] as Map<String, dynamic>).entries.take(5).map((entry) =>
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 2),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(entry.key),
                        Text('${entry.value}'),
                      ],
                    ),
                  ),
                )),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Close'),
            ),
          ],
        ),
      );
    } catch (e) {
      // Close loading dialog if open
      Navigator.pop(context);

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error loading analytics: $e'),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  /// Build analytics item widget
  Widget _buildAnalyticsItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label, style: const TextStyle(fontWeight: FontWeight.w500)),
          Text(value, style: const TextStyle(fontWeight: FontWeight.bold)),
        ],
      ),
    );
  }

  /// Refresh detection history from storage
  Future<void> _refreshHistory() async {
    await _loadDetectionHistory();
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      backgroundColor:
          isDark ? AppTheme.backgroundDark : AppTheme.backgroundLight,
      appBar: AppBar(
        title: Text(
          _isMultiSelectMode
              ? '${_selectedItems.length} Selected'
              : 'Detection History',
          style: AppTheme.lightTheme.textTheme.titleLarge?.copyWith(
            color:
                isDark ? AppTheme.textPrimaryDark : AppTheme.textPrimaryLight,
          ),
        ),
        backgroundColor:
            isDark ? AppTheme.backgroundDark : AppTheme.backgroundLight,
        elevation: 0,
        leading: _isMultiSelectMode
            ? IconButton(
                onPressed: _toggleMultiSelectMode,
                icon: CustomIconWidget(
                  iconName: 'close',
                  color: isDark
                      ? AppTheme.textPrimaryDark
                      : AppTheme.textPrimaryLight,
                  size: 24,
                ),
              )
            : IconButton(
                onPressed: () => Navigator.pop(context),
                icon: CustomIconWidget(
                  iconName: 'arrow_back',
                  color: isDark
                      ? AppTheme.textPrimaryDark
                      : AppTheme.textPrimaryLight,
                  size: 24,
                ),
              ),
        actions: [
          if (_isMultiSelectMode) ...[
            if (_selectedItems.isNotEmpty) ...[
              IconButton(
                onPressed: _shareSelectedItems,
                icon: CustomIconWidget(
                  iconName: 'share',
                  color: isDark ? AppTheme.primaryDark : AppTheme.primaryLight,
                  size: 24,
                ),
              ),
              IconButton(
                onPressed: _deleteSelectedItems,
                icon: CustomIconWidget(
                  iconName: 'delete',
                  color: isDark ? AppTheme.errorDark : AppTheme.errorLight,
                  size: 24,
                ),
              ),
            ],
          ] else ...[
            IconButton(
              onPressed: _exportHistory,
              icon: CustomIconWidget(
                iconName: 'download',
                color: isDark
                    ? AppTheme.textPrimaryDark
                    : AppTheme.textPrimaryLight,
                size: 24,
              ),
            ),
            IconButton(
              onPressed: _showFilterBottomSheet,
              icon: CustomIconWidget(
                iconName: 'filter_list',
                color: isDark
                    ? AppTheme.textPrimaryDark
                    : AppTheme.textPrimaryLight,
                size: 24,
              ),
            ),
            IconButton(
              onPressed: _toggleMultiSelectMode,
              icon: CustomIconWidget(
                iconName: 'checklist',
                color: isDark
                    ? AppTheme.textPrimaryDark
                    : AppTheme.textPrimaryLight,
                size: 24,
              ),
            ),
          ],
        ],
      ),
      body: SafeArea(
        child: Column(
          children: [
            // Search Bar
            Container(
              margin: EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.h),
              decoration: BoxDecoration(
                color: isDark ? AppTheme.surfaceDark : AppTheme.surfaceLight,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: isDark ? AppTheme.borderDark : AppTheme.borderLight,
                ),
              ),
              child: TextField(
                controller: _searchController,
                decoration: InputDecoration(
                  hintText: 'Search detections...',
                  prefixIcon: Padding(
                    padding: EdgeInsets.all(3.w),
                    child: CustomIconWidget(
                      iconName: 'search',
                      color: isDark
                          ? AppTheme.textSecondaryDark
                          : AppTheme.textSecondaryLight,
                      size: 20,
                    ),
                  ),
                  suffixIcon: _isSearching
                      ? IconButton(
                          onPressed: () {
                            _searchController.clear();
                            setState(() {
                              _isSearching = false;
                              _filteredHistory = List.from(_detectionHistory);
                            });
                          },
                          icon: CustomIconWidget(
                            iconName: 'clear',
                            color: isDark
                                ? AppTheme.textSecondaryDark
                                : AppTheme.textSecondaryLight,
                            size: 20,
                          ),
                        )
                      : null,
                  border: InputBorder.none,
                  contentPadding:
                      EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.5.h),
                ),
                style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                  color: isDark
                      ? AppTheme.textPrimaryDark
                      : AppTheme.textPrimaryLight,
                ),
              ),
            ),

            // Content
            Expanded(
              child: _isLoading
                  ? Center(
                      child: CircularProgressIndicator(
                        color: isDark
                            ? AppTheme.primaryDark
                            : AppTheme.primaryLight,
                      ),
                    )
                  : _filteredHistory.isEmpty
                      ? EmptyStateWidget(
                          onStartDetecting: () {
                            Navigator.pushNamed(
                                context, '/camera-detection-screen');
                          },
                        )
                      : RefreshIndicator(
                          onRefresh: _refreshHistory,
                          color: isDark
                              ? AppTheme.primaryDark
                              : AppTheme.primaryLight,
                          child: ListView.builder(
                            controller: _scrollController,
                            padding: EdgeInsets.symmetric(
                                horizontal: 4.w, vertical: 1.h),
                            itemCount: _filteredHistory.length,
                            itemBuilder: (context, index) {
                              final detection = _filteredHistory[index];
                              final isSelected =
                                  _selectedItems.contains(detection['id']);

                              return DetectionHistoryCardWidget(
                                detection: detection,
                                isMultiSelectMode: _isMultiSelectMode,
                                isSelected: isSelected,
                                onTap: () {
                                  if (_isMultiSelectMode) {
                                    _toggleItemSelection(detection['id']);
                                  } else {
                                    Navigator.pushNamed(
                                      context,
                                      '/detection-detail-screen',
                                      arguments: detection,
                                    );
                                  }
                                },
                                onLongPress: () {
                                  if (!_isMultiSelectMode) {
                                    _toggleMultiSelectMode();
                                    _toggleItemSelection(detection['id']);
                                  }
                                },
                                onShare: () {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    const SnackBar(
                                        content: Text('Detection shared')),
                                  );
                                },
                                onDelete: () {
                                  showDialog(
                                    context: context,
                                    builder: (context) => AlertDialog(
                                      title: Text(
                                        'Delete Detection',
                                        style: AppTheme
                                            .lightTheme.textTheme.titleLarge,
                                      ),
                                      content: Text(
                                        'Are you sure you want to delete this detection?',
                                        style: AppTheme
                                            .lightTheme.textTheme.bodyMedium,
                                      ),
                                      actions: [
                                        TextButton(
                                          onPressed: () =>
                                              Navigator.pop(context),
                                          child: const Text('Cancel'),
                                        ),
                                        ElevatedButton(
                                          onPressed: () {
                                            setState(() {
                                              _detectionHistory.removeWhere(
                                                  (d) =>
                                                      d['id'] ==
                                                      detection['id']);
                                              _filteredHistory.removeWhere(
                                                  (d) =>
                                                      d['id'] ==
                                                      detection['id']);
                                            });
                                            Navigator.pop(context);
                                            ScaffoldMessenger.of(context)
                                                .showSnackBar(
                                              const SnackBar(
                                                  content: Text(
                                                      'Detection deleted')),
                                            );
                                          },
                                          child: const Text('Delete'),
                                        ),
                                      ],
                                    ),
                                  );
                                },
                              );
                            },
                          ),
                        ),
            ),
          ],
        ),
      ),
      floatingActionButton: _filteredHistory.isNotEmpty && !_isMultiSelectMode
          ? FloatingActionButton.extended(
              onPressed: () {
                Navigator.pushNamed(context, '/camera-detection-screen');
              },
              backgroundColor:
                  isDark ? AppTheme.primaryDark : AppTheme.primaryLight,
              foregroundColor: isDark ? Colors.black : Colors.white,
              icon: CustomIconWidget(
                iconName: 'camera_alt',
                color: isDark ? Colors.black : Colors.white,
                size: 24,
              ),
              label: Text(
                'Detect Objects',
                style: AppTheme.lightTheme.textTheme.labelLarge?.copyWith(
                  color: isDark ? Colors.black : Colors.white,
                ),
              ),
            )
          : null,
    );
  }
}
