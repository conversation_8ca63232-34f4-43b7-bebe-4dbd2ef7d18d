import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../core/app_export.dart';
import './widgets/detection_history_card_widget.dart';
import './widgets/empty_state_widget.dart';
import './widgets/search_filter_widget.dart';

class DetectionHistoryScreen extends StatefulWidget {
  const DetectionHistoryScreen({super.key});

  @override
  State<DetectionHistoryScreen> createState() => _DetectionHistoryScreenState();
}

class _DetectionHistoryScreenState extends State<DetectionHistoryScreen> {
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  List<Map<String, dynamic>> _detectionHistory = [];
  List<Map<String, dynamic>> _filteredHistory = [];
  bool _isLoading = false;
  bool _isSearching = false;
  bool _isMultiSelectMode = false;
  final Set<int> _selectedItems = {};
  String _selectedFilter = 'All';
  DateTimeRange? _selectedDateRange;
  double _confidenceThreshold = 0.0;

  @override
  void initState() {
    super.initState();
    _loadDetectionHistory();
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _loadDetectionHistory() {
    setState(() {
      _isLoading = true;
    });

    // Mock detection history data
    _detectionHistory = [
      {
        "id": 1,
        "timestamp": DateTime.now().subtract(const Duration(hours: 2)),
        "imageUrl":
            "https://images.pexels.com/photos/1108099/pexels-photo-1108099.jpeg",
        "detectedObjects": [
          {"name": "Person", "confidence": 0.95},
          {"name": "Bicycle", "confidence": 0.87},
        ],
        "location": "Living Room",
        "totalObjects": 2,
      },
      {
        "id": 2,
        "timestamp": DateTime.now().subtract(const Duration(hours: 5)),
        "imageUrl":
            "https://images.pexels.com/photos/416978/pexels-photo-416978.jpeg",
        "detectedObjects": [
          {"name": "Cat", "confidence": 0.92},
          {"name": "Sofa", "confidence": 0.78},
        ],
        "location": "Bedroom",
        "totalObjects": 2,
      },
      {
        "id": 3,
        "timestamp": DateTime.now().subtract(const Duration(days: 1)),
        "imageUrl":
            "https://images.pexels.com/photos/1640777/pexels-photo-1640777.jpeg",
        "detectedObjects": [
          {"name": "Car", "confidence": 0.89},
          {"name": "Traffic Light", "confidence": 0.76},
          {"name": "Building", "confidence": 0.83},
        ],
        "location": "Street View",
        "totalObjects": 3,
      },
      {
        "id": 4,
        "timestamp": DateTime.now().subtract(const Duration(days: 2)),
        "imageUrl":
            "https://images.pexels.com/photos/1640772/pexels-photo-1640772.jpeg",
        "detectedObjects": [
          {"name": "Dog", "confidence": 0.94},
          {"name": "Ball", "confidence": 0.71},
        ],
        "location": "Park",
        "totalObjects": 2,
      },
      {
        "id": 5,
        "timestamp": DateTime.now().subtract(const Duration(days: 3)),
        "imageUrl":
            "https://images.pexels.com/photos/1108117/pexels-photo-1108117.jpeg",
        "detectedObjects": [
          {"name": "Phone", "confidence": 0.88},
          {"name": "Laptop", "confidence": 0.85},
          {"name": "Cup", "confidence": 0.79},
        ],
        "location": "Office",
        "totalObjects": 3,
      },
    ];

    _filteredHistory = List.from(_detectionHistory);

    setState(() {
      _isLoading = false;
    });
  }

  void _onSearchChanged() {
    final query = _searchController.text.toLowerCase();
    setState(() {
      _isSearching = query.isNotEmpty;
      _filteredHistory = _detectionHistory.where((detection) {
        final objects = (detection['detectedObjects'] as List)
            .map((obj) => (obj as Map<String, dynamic>)['name'] as String)
            .join(' ')
            .toLowerCase();
        final location = (detection['location'] as String).toLowerCase();
        return objects.contains(query) || location.contains(query);
      }).toList();
    });
  }

  void _applyFilters() {
    setState(() {
      _filteredHistory = _detectionHistory.where((detection) {
        // Date range filter
        if (_selectedDateRange != null) {
          final detectionDate = detection['timestamp'] as DateTime;
          if (detectionDate.isBefore(_selectedDateRange!.start) ||
              detectionDate.isAfter(_selectedDateRange!.end)) {
            return false;
          }
        }

        // Confidence threshold filter
        final objects = detection['detectedObjects'] as List;
        final hasHighConfidence = objects.any((obj) {
          final confidence =
              (obj as Map<String, dynamic>)['confidence'] as double;
          return confidence >= _confidenceThreshold;
        });
        if (!hasHighConfidence) return false;

        // Category filter
        if (_selectedFilter != 'All') {
          final hasCategory = objects.any((obj) {
            final name = (obj as Map<String, dynamic>)['name'] as String;
            return _getCategoryForObject(name) == _selectedFilter;
          });
          if (!hasCategory) return false;
        }

        return true;
      }).toList();
    });
  }

  String _getCategoryForObject(String objectName) {
    const categories = {
      'Person': 'People',
      'Cat': 'Animals',
      'Dog': 'Animals',
      'Car': 'Vehicles',
      'Bicycle': 'Vehicles',
      'Phone': 'Electronics',
      'Laptop': 'Electronics',
      'Cup': 'Objects',
      'Ball': 'Objects',
      'Sofa': 'Furniture',
      'Traffic Light': 'Objects',
      'Building': 'Structures',
    };
    return categories[objectName] ?? 'Objects';
  }

  void _showFilterBottomSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => SearchFilterWidget(
        selectedFilter: _selectedFilter,
        selectedDateRange: _selectedDateRange,
        confidenceThreshold: _confidenceThreshold,
        onFilterChanged: (filter, dateRange, confidence) {
          setState(() {
            _selectedFilter = filter;
            _selectedDateRange = dateRange;
            _confidenceThreshold = confidence;
          });
          _applyFilters();
        },
      ),
    );
  }

  void _toggleMultiSelectMode() {
    setState(() {
      _isMultiSelectMode = !_isMultiSelectMode;
      if (!_isMultiSelectMode) {
        _selectedItems.clear();
      }
    });
  }

  void _toggleItemSelection(int id) {
    setState(() {
      if (_selectedItems.contains(id)) {
        _selectedItems.remove(id);
      } else {
        _selectedItems.add(id);
      }
    });
  }

  void _deleteSelectedItems() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Delete Selected Items',
          style: AppTheme.lightTheme.textTheme.titleLarge,
        ),
        content: Text(
          'Are you sure you want to delete ${_selectedItems.length} selected detection${_selectedItems.length > 1 ? 's' : ''}?',
          style: AppTheme.lightTheme.textTheme.bodyMedium,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              setState(() {
                _detectionHistory.removeWhere(
                    (detection) => _selectedItems.contains(detection['id']));
                _filteredHistory.removeWhere(
                    (detection) => _selectedItems.contains(detection['id']));
                _selectedItems.clear();
                _isMultiSelectMode = false;
              });
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Selected items deleted')),
              );
            },
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _shareSelectedItems() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
            'Sharing ${_selectedItems.length} detection${_selectedItems.length > 1 ? 's' : ''}'),
      ),
    );
  }

  Future<void> _refreshHistory() async {
    await Future.delayed(const Duration(seconds: 1));
    _loadDetectionHistory();
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      backgroundColor:
          isDark ? AppTheme.backgroundDark : AppTheme.backgroundLight,
      appBar: AppBar(
        title: Text(
          _isMultiSelectMode
              ? '${_selectedItems.length} Selected'
              : 'Detection History',
          style: AppTheme.lightTheme.textTheme.titleLarge?.copyWith(
            color:
                isDark ? AppTheme.textPrimaryDark : AppTheme.textPrimaryLight,
          ),
        ),
        backgroundColor:
            isDark ? AppTheme.backgroundDark : AppTheme.backgroundLight,
        elevation: 0,
        leading: _isMultiSelectMode
            ? IconButton(
                onPressed: _toggleMultiSelectMode,
                icon: CustomIconWidget(
                  iconName: 'close',
                  color: isDark
                      ? AppTheme.textPrimaryDark
                      : AppTheme.textPrimaryLight,
                  size: 24,
                ),
              )
            : IconButton(
                onPressed: () => Navigator.pop(context),
                icon: CustomIconWidget(
                  iconName: 'arrow_back',
                  color: isDark
                      ? AppTheme.textPrimaryDark
                      : AppTheme.textPrimaryLight,
                  size: 24,
                ),
              ),
        actions: [
          if (_isMultiSelectMode) ...[
            if (_selectedItems.isNotEmpty) ...[
              IconButton(
                onPressed: _shareSelectedItems,
                icon: CustomIconWidget(
                  iconName: 'share',
                  color: isDark ? AppTheme.primaryDark : AppTheme.primaryLight,
                  size: 24,
                ),
              ),
              IconButton(
                onPressed: _deleteSelectedItems,
                icon: CustomIconWidget(
                  iconName: 'delete',
                  color: isDark ? AppTheme.errorDark : AppTheme.errorLight,
                  size: 24,
                ),
              ),
            ],
          ] else ...[
            IconButton(
              onPressed: _showFilterBottomSheet,
              icon: CustomIconWidget(
                iconName: 'filter_list',
                color: isDark
                    ? AppTheme.textPrimaryDark
                    : AppTheme.textPrimaryLight,
                size: 24,
              ),
            ),
            IconButton(
              onPressed: _toggleMultiSelectMode,
              icon: CustomIconWidget(
                iconName: 'checklist',
                color: isDark
                    ? AppTheme.textPrimaryDark
                    : AppTheme.textPrimaryLight,
                size: 24,
              ),
            ),
          ],
        ],
      ),
      body: SafeArea(
        child: Column(
          children: [
            // Search Bar
            Container(
              margin: EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.h),
              decoration: BoxDecoration(
                color: isDark ? AppTheme.surfaceDark : AppTheme.surfaceLight,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: isDark ? AppTheme.borderDark : AppTheme.borderLight,
                ),
              ),
              child: TextField(
                controller: _searchController,
                decoration: InputDecoration(
                  hintText: 'Search detections...',
                  prefixIcon: Padding(
                    padding: EdgeInsets.all(3.w),
                    child: CustomIconWidget(
                      iconName: 'search',
                      color: isDark
                          ? AppTheme.textSecondaryDark
                          : AppTheme.textSecondaryLight,
                      size: 20,
                    ),
                  ),
                  suffixIcon: _isSearching
                      ? IconButton(
                          onPressed: () {
                            _searchController.clear();
                            setState(() {
                              _isSearching = false;
                              _filteredHistory = List.from(_detectionHistory);
                            });
                          },
                          icon: CustomIconWidget(
                            iconName: 'clear',
                            color: isDark
                                ? AppTheme.textSecondaryDark
                                : AppTheme.textSecondaryLight,
                            size: 20,
                          ),
                        )
                      : null,
                  border: InputBorder.none,
                  contentPadding:
                      EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.5.h),
                ),
                style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                  color: isDark
                      ? AppTheme.textPrimaryDark
                      : AppTheme.textPrimaryLight,
                ),
              ),
            ),

            // Content
            Expanded(
              child: _isLoading
                  ? Center(
                      child: CircularProgressIndicator(
                        color: isDark
                            ? AppTheme.primaryDark
                            : AppTheme.primaryLight,
                      ),
                    )
                  : _filteredHistory.isEmpty
                      ? EmptyStateWidget(
                          onStartDetecting: () {
                            Navigator.pushNamed(
                                context, '/camera-detection-screen');
                          },
                        )
                      : RefreshIndicator(
                          onRefresh: _refreshHistory,
                          color: isDark
                              ? AppTheme.primaryDark
                              : AppTheme.primaryLight,
                          child: ListView.builder(
                            controller: _scrollController,
                            padding: EdgeInsets.symmetric(
                                horizontal: 4.w, vertical: 1.h),
                            itemCount: _filteredHistory.length,
                            itemBuilder: (context, index) {
                              final detection = _filteredHistory[index];
                              final isSelected =
                                  _selectedItems.contains(detection['id']);

                              return DetectionHistoryCardWidget(
                                detection: detection,
                                isMultiSelectMode: _isMultiSelectMode,
                                isSelected: isSelected,
                                onTap: () {
                                  if (_isMultiSelectMode) {
                                    _toggleItemSelection(detection['id']);
                                  } else {
                                    Navigator.pushNamed(
                                      context,
                                      '/detection-detail-screen',
                                      arguments: detection,
                                    );
                                  }
                                },
                                onLongPress: () {
                                  if (!_isMultiSelectMode) {
                                    _toggleMultiSelectMode();
                                    _toggleItemSelection(detection['id']);
                                  }
                                },
                                onShare: () {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    const SnackBar(
                                        content: Text('Detection shared')),
                                  );
                                },
                                onDelete: () {
                                  showDialog(
                                    context: context,
                                    builder: (context) => AlertDialog(
                                      title: Text(
                                        'Delete Detection',
                                        style: AppTheme
                                            .lightTheme.textTheme.titleLarge,
                                      ),
                                      content: Text(
                                        'Are you sure you want to delete this detection?',
                                        style: AppTheme
                                            .lightTheme.textTheme.bodyMedium,
                                      ),
                                      actions: [
                                        TextButton(
                                          onPressed: () =>
                                              Navigator.pop(context),
                                          child: const Text('Cancel'),
                                        ),
                                        ElevatedButton(
                                          onPressed: () {
                                            setState(() {
                                              _detectionHistory.removeWhere(
                                                  (d) =>
                                                      d['id'] ==
                                                      detection['id']);
                                              _filteredHistory.removeWhere(
                                                  (d) =>
                                                      d['id'] ==
                                                      detection['id']);
                                            });
                                            Navigator.pop(context);
                                            ScaffoldMessenger.of(context)
                                                .showSnackBar(
                                              const SnackBar(
                                                  content: Text(
                                                      'Detection deleted')),
                                            );
                                          },
                                          child: const Text('Delete'),
                                        ),
                                      ],
                                    ),
                                  );
                                },
                              );
                            },
                          ),
                        ),
            ),
          ],
        ),
      ),
      floatingActionButton: _filteredHistory.isNotEmpty && !_isMultiSelectMode
          ? FloatingActionButton.extended(
              onPressed: () {
                Navigator.pushNamed(context, '/camera-detection-screen');
              },
              backgroundColor:
                  isDark ? AppTheme.primaryDark : AppTheme.primaryLight,
              foregroundColor: isDark ? Colors.black : Colors.white,
              icon: CustomIconWidget(
                iconName: 'camera_alt',
                color: isDark ? Colors.black : Colors.white,
                size: 24,
              ),
              label: Text(
                'Detect Objects',
                style: AppTheme.lightTheme.textTheme.labelLarge?.copyWith(
                  color: isDark ? Colors.black : Colors.white,
                ),
              ),
            )
          : null,
    );
  }
}
