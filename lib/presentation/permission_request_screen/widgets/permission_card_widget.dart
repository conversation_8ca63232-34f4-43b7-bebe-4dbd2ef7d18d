import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

class PermissionCardWidget extends StatelessWidget {
  final String icon;
  final String title;
  final String description;
  final bool isGranted;
  final bool isLoading;

  const PermissionCardWidget({
    super.key,
    required this.icon,
    required this.title,
    required this.description,
    this.isGranted = false,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: AppTheme.lightTheme.colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: isGranted
              ? AppTheme.getSuccessColor(true).withValues(alpha: 0.3)
              : AppTheme.lightTheme.colorScheme.outline.withValues(alpha: 0.2),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color:
                AppTheme.lightTheme.colorScheme.shadow.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          _buildIconContainer(),
          SizedBox(width: 4.w),
          Expanded(
            child: _buildContent(),
          ),
          _buildStatusIndicator(),
        ],
      ),
    );
  }

  Widget _buildIconContainer() {
    return Container(
      width: 15.w,
      height: 15.w,
      decoration: BoxDecoration(
        color: isGranted
            ? AppTheme.getSuccessColor(true).withValues(alpha: 0.1)
            : AppTheme.lightTheme.colorScheme.primary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: CustomIconWidget(
        iconName: icon,
        color: isGranted
            ? AppTheme.getSuccessColor(true)
            : AppTheme.lightTheme.colorScheme.primary,
        size: 7.w,
      ),
    );
  }

  Widget _buildContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: isGranted
                ? AppTheme.getSuccessColor(true)
                : AppTheme.lightTheme.colorScheme.onSurface,
          ),
        ),
        SizedBox(height: 0.5.h),
        Text(
          description,
          style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
            color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
            height: 1.3,
          ),
        ),
      ],
    );
  }

  Widget _buildStatusIndicator() {
    if (isLoading) {
      return SizedBox(
        width: 6.w,
        height: 6.w,
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(
            AppTheme.lightTheme.colorScheme.primary,
          ),
        ),
      );
    }

    if (isGranted) {
      return Container(
        width: 8.w,
        height: 8.w,
        decoration: BoxDecoration(
          color: AppTheme.getSuccessColor(true),
          shape: BoxShape.circle,
        ),
        child: CustomIconWidget(
          iconName: 'check',
          color: Colors.white,
          size: 5.w,
        ),
      );
    }

    return Container(
      width: 8.w,
      height: 8.w,
      decoration: BoxDecoration(
        color: Colors.transparent,
        shape: BoxShape.circle,
        border: Border.all(
          color: AppTheme.lightTheme.colorScheme.outline.withValues(alpha: 0.3),
          width: 2,
        ),
      ),
    );
  }
}
