import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../core/app_export.dart';
import './widgets/permission_card_widget.dart';
import './widgets/privacy_explanation_widget.dart';

class PermissionRequestScreen extends StatefulWidget {
  const PermissionRequestScreen({super.key});

  @override
  State<PermissionRequestScreen> createState() =>
      _PermissionRequestScreenState();
}

class _PermissionRequestScreenState extends State<PermissionRequestScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  Map<String, bool> permissionStates = {
    'camera': false,
    'storage': false,
    'microphone': false,
  };

  bool _isRequestingPermissions = false;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _startEntryAnimation();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));
  }

  void _startEntryAnimation() {
    Future.delayed(const Duration(milliseconds: 200), () {
      if (mounted) {
        _animationController.forward();
      }
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _requestPermissions() async {
    setState(() {
      _isRequestingPermissions = true;
    });

    // Simulate permission request sequence
    await _requestCameraPermission();
    await Future.delayed(const Duration(milliseconds: 500));
    await _requestStoragePermission();
    await Future.delayed(const Duration(milliseconds: 500));
    await _requestMicrophonePermission();

    setState(() {
      _isRequestingPermissions = false;
    });

    // Check if all permissions are granted
    if (_allPermissionsGranted()) {
      _showSuccessAnimation();
    }
  }

  Future<void> _requestCameraPermission() async {
    // Simulate camera permission request
    await Future.delayed(const Duration(milliseconds: 1000));
    setState(() {
      permissionStates['camera'] = true; // Mock granted
    });
  }

  Future<void> _requestStoragePermission() async {
    // Simulate storage permission request
    await Future.delayed(const Duration(milliseconds: 1000));
    setState(() {
      permissionStates['storage'] = true; // Mock granted
    });
  }

  Future<void> _requestMicrophonePermission() async {
    // Simulate microphone permission request
    await Future.delayed(const Duration(milliseconds: 1000));
    setState(() {
      permissionStates['microphone'] = true; // Mock granted
    });
  }

  bool _allPermissionsGranted() {
    return permissionStates.values.every((granted) => granted);
  }

  void _showSuccessAnimation() {
    // Show celebration micro-interaction
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.transparent,
        elevation: 0,
        content: Container(
          width: 80.w,
          height: 30.h,
          decoration: BoxDecoration(
            color: AppTheme.lightTheme.colorScheme.surface,
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: 20.w,
                height: 20.w,
                decoration: BoxDecoration(
                  color: AppTheme.getSuccessColor(true).withValues(alpha: 0.1),
                  shape: BoxShape.circle,
                ),
                child: CustomIconWidget(
                  iconName: 'check_circle',
                  color: AppTheme.getSuccessColor(true),
                  size: 10.w,
                ),
              ),
              SizedBox(height: 2.h),
              Text(
                'Permissions Granted!',
                style: AppTheme.lightTheme.textTheme.titleLarge?.copyWith(
                  color: AppTheme.getSuccessColor(true),
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 1.h),
              Text(
                'You\'re all set to start detecting objects',
                style: AppTheme.lightTheme.textTheme.bodyMedium,
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );

    // Navigate to camera detection screen after delay
    Future.delayed(const Duration(milliseconds: 2000), () {
      if (mounted) {
        Navigator.of(context).pop(); // Close dialog
        Navigator.pushReplacementNamed(context, '/camera-detection-screen');
      }
    });
  }

  void _showPrivacyExplanation() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => PrivacyExplanationWidget(),
    );
  }

  void _continueWithLimitedFeatures() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Limited Features',
          style: AppTheme.lightTheme.textTheme.titleLarge,
        ),
        content: Text(
          'Some features may not work properly without the required permissions. You can grant permissions later in Settings.',
          style: AppTheme.lightTheme.textTheme.bodyMedium,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.pushReplacementNamed(
                  context, '/camera-detection-screen');
            },
            child: Text('Continue'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.lightTheme.scaffoldBackgroundColor,
      body: SafeArea(
        child: AnimatedBuilder(
          animation: _animationController,
          builder: (context, child) {
            return FadeTransition(
              opacity: _fadeAnimation,
              child: SlideTransition(
                position: _slideAnimation,
                child: _buildContent(),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildContent() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 6.w),
      child: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  SizedBox(height: 4.h),
                  _buildHeader(),
                  SizedBox(height: 4.h),
                  _buildPermissionCards(),
                  SizedBox(height: 3.h),
                  _buildLearnMoreButton(),
                ],
              ),
            ),
          ),
          _buildBottomActions(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      children: [
        Container(
          width: 25.w,
          height: 25.w,
          decoration: BoxDecoration(
            color:
                AppTheme.lightTheme.colorScheme.primary.withValues(alpha: 0.1),
            shape: BoxShape.circle,
          ),
          child: CustomIconWidget(
            iconName: 'security',
            color: AppTheme.lightTheme.colorScheme.primary,
            size: 12.w,
          ),
        ),
        SizedBox(height: 3.h),
        Text(
          'Permissions Required',
          style: AppTheme.lightTheme.textTheme.headlineMedium?.copyWith(
            fontWeight: FontWeight.w700,
          ),
          textAlign: TextAlign.center,
        ),
        SizedBox(height: 1.h),
        Text(
          'Visionary needs these permissions to provide the best object detection experience',
          style: AppTheme.lightTheme.textTheme.bodyLarge?.copyWith(
            color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildPermissionCards() {
    final permissions = [
      {
        'icon': 'camera_alt',
        'title': 'Camera Access',
        'description': 'Camera access for real-time object detection',
        'key': 'camera',
      },
      {
        'icon': 'folder',
        'title': 'Storage Access',
        'description': 'Storage for saving snapshots and custom models',
        'key': 'storage',
      },
      {
        'icon': 'mic',
        'title': 'Microphone Access',
        'description': 'Microphone for voice-based object announcements',
        'key': 'microphone',
      },
    ];

    return Column(
      children: permissions.map((permission) {
        return Padding(
          padding: EdgeInsets.only(bottom: 2.h),
          child: PermissionCardWidget(
            icon: permission['icon'] as String,
            title: permission['title'] as String,
            description: permission['description'] as String,
            isGranted: permissionStates[permission['key']] ?? false,
            isLoading: _isRequestingPermissions,
          ),
        );
      }).toList(),
    );
  }

  Widget _buildLearnMoreButton() {
    return TextButton.icon(
      onPressed: _showPrivacyExplanation,
      icon: CustomIconWidget(
        iconName: 'info_outline',
        color: AppTheme.lightTheme.colorScheme.primary,
        size: 5.w,
      ),
      label: Text(
        'Learn More About Privacy',
        style: AppTheme.lightTheme.textTheme.labelLarge?.copyWith(
          color: AppTheme.lightTheme.colorScheme.primary,
        ),
      ),
    );
  }

  Widget _buildBottomActions() {
    return Padding(
      padding: EdgeInsets.only(bottom: 2.h),
      child: Column(
        children: [
          SizedBox(
            width: double.infinity,
            height: 7.h,
            child: ElevatedButton(
              onPressed: _isRequestingPermissions ? null : _requestPermissions,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.lightTheme.colorScheme.primary,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: _isRequestingPermissions
                  ? Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(
                          width: 5.w,
                          height: 5.w,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor:
                                AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        ),
                        SizedBox(width: 3.w),
                        Text(
                          'Requesting Permissions...',
                          style: AppTheme.lightTheme.textTheme.labelLarge
                              ?.copyWith(
                            color: Colors.white,
                          ),
                        ),
                      ],
                    )
                  : Text(
                      'Grant Permissions',
                      style: AppTheme.lightTheme.textTheme.labelLarge?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
            ),
          ),
          SizedBox(height: 1.h),
          TextButton(
            onPressed: _continueWithLimitedFeatures,
            child: Text(
              'Continue with Limited Features',
              style: AppTheme.lightTheme.textTheme.labelMedium?.copyWith(
                color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
