import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:sizer/sizer.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../core/app_export.dart';
import './widgets/onboarding_page_widget.dart';
import './widgets/page_indicator_widget.dart';

class OnboardingFlow extends StatefulWidget {
  const OnboardingFlow({super.key});

  @override
  State<OnboardingFlow> createState() => _OnboardingFlowState();
}

class _OnboardingFlowState extends State<OnboardingFlow>
    with TickerProviderStateMixin {
  late PageController _pageController;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  int _currentPage = 0;

  final List<Map<String, dynamic>> _onboardingData = [
    {
      "title": "Real-Time Object Detection",
      "description":
          "Instantly identify objects around you with advanced AI technology. Point your camera and see results in real-time with confidence scores.",
      "illustration":
          "https://images.unsplash.com/photo-**********-fcd25c85cd64?fm=jpg&q=60&w=3000&ixlib=rb-4.0.3",
      "features": [
        {"icon": "camera_alt", "text": "Live Camera Feed"},
        {"icon": "speed", "text": "Instant Recognition"},
        {"icon": "analytics", "text": "Confidence Scores"}
      ]
    },
    {
      "title": "Works Completely Offline",
      "description":
          "No internet required! All AI processing happens directly on your device, ensuring privacy and reliability anywhere you go.",
      "illustration":
          "https://images.unsplash.com/photo-1516321318423-f06f85e504b3?fm=jpg&q=60&w=3000&ixlib=rb-4.0.3",
      "features": [
        {"icon": "offline_bolt", "text": "No Internet Needed"},
        {"icon": "security", "text": "Complete Privacy"},
        {"icon": "devices", "text": "On-Device Processing"}
      ]
    },
    {
      "title": "Accessibility & Voice Support",
      "description":
          "Designed for everyone. Voice readout feature helps visually impaired users identify objects with audio descriptions.",
      "illustration":
          "https://images.unsplash.com/photo-1573164713714-d95e436ab8d6?fm=jpg&q=60&w=3000&ixlib=rb-4.0.3",
      "features": [
        {"icon": "record_voice_over", "text": "Voice Readout"},
        {"icon": "accessibility", "text": "Universal Design"},
        {"icon": "hearing", "text": "Audio Descriptions"}
      ]
    }
  ];

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    _animationController.forward();
  }

  @override
  void dispose() {
    _pageController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  void _nextPage() {
    if (_currentPage < _onboardingData.length - 1) {
      HapticFeedback.lightImpact();
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } else {
      _navigateToPermissionScreen();
    }
  }

  void _skipOnboarding() {
    HapticFeedback.mediumImpact();
    _navigateToPermissionScreen();
  }

  void _navigateToPermissionScreen() async {
    // Mark that user has seen onboarding
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('has_seen_onboarding', true);

    Navigator.pushReplacementNamed(context, '/permission-request-screen');
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: isDark
          ? AppTheme.darkTheme.scaffoldBackgroundColor
          : AppTheme.lightTheme.scaffoldBackgroundColor,
      body: SafeArea(
        child: Column(
          children: [
            // Skip button
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.h),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: _skipOnboarding,
                    child: Text(
                      'Skip',
                      style: Theme.of(context).textTheme.labelLarge?.copyWith(
                            color: isDark
                                ? AppTheme
                                    .darkTheme.colorScheme.onSurfaceVariant
                                : AppTheme
                                    .lightTheme.colorScheme.onSurfaceVariant,
                          ),
                    ),
                  ),
                ],
              ),
            ),

            // PageView content
            Expanded(
              child: FadeTransition(
                opacity: _fadeAnimation,
                child: PageView.builder(
                  controller: _pageController,
                  onPageChanged: (index) {
                    setState(() {
                      _currentPage = index;
                    });
                    HapticFeedback.selectionClick();
                  },
                  itemCount: _onboardingData.length,
                  itemBuilder: (context, index) {
                    return OnboardingPageWidget(
                      data: _onboardingData[index],
                      isActive: index == _currentPage,
                    );
                  },
                ),
              ),
            ),

            // Bottom section with indicator and button
            Container(
              padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 3.h),
              child: Column(
                children: [
                  // Page indicator
                  PageIndicatorWidget(
                    currentPage: _currentPage,
                    totalPages: _onboardingData.length,
                  ),

                  SizedBox(height: 4.h),

                  // Action buttons
                  Row(
                    children: [
                      // Previous button (only show after first page)
                      if (_currentPage > 0)
                        Expanded(
                          flex: 1,
                          child: OutlinedButton(
                            onPressed: () {
                              HapticFeedback.lightImpact();
                              _pageController.previousPage(
                                duration: const Duration(milliseconds: 300),
                                curve: Curves.easeInOut,
                              );
                            },
                            style: OutlinedButton.styleFrom(
                              padding: EdgeInsets.symmetric(vertical: 2.h),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                            child: Text(
                              'Previous',
                              style: Theme.of(context).textTheme.labelLarge,
                            ),
                          ),
                        ),

                      if (_currentPage > 0) SizedBox(width: 4.w),

                      // Next/Get Started button
                      Expanded(
                        flex: _currentPage > 0 ? 2 : 1,
                        child: ElevatedButton(
                          onPressed: _nextPage,
                          style: ElevatedButton.styleFrom(
                            padding: EdgeInsets.symmetric(vertical: 2.h),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            elevation: 2,
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                _currentPage == _onboardingData.length - 1
                                    ? 'Get Started'
                                    : 'Next',
                                style: Theme.of(context)
                                    .textTheme
                                    .labelLarge
                                    ?.copyWith(
                                      color:
                                          isDark ? Colors.black : Colors.white,
                                      fontWeight: FontWeight.w600,
                                    ),
                              ),
                              SizedBox(width: 2.w),
                              CustomIconWidget(
                                iconName:
                                    _currentPage == _onboardingData.length - 1
                                        ? 'rocket_launch'
                                        : 'arrow_forward',
                                color: isDark ? Colors.black : Colors.white,
                                size: 18,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
