import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

class InteractiveDemoWidget extends StatefulWidget {
  final int pageIndex;

  const InteractiveDemoWidget({
    super.key,
    required this.pageIndex,
  });

  @override
  State<InteractiveDemoWidget> createState() => _InteractiveDemoWidgetState();
}

class _InteractiveDemoWidgetState extends State<InteractiveDemoWidget>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _bounceController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _bounceAnimation;
  
  bool _showDetection = false;
  List<Map<String, dynamic>> _detectedObjects = [];

  @override
  void initState() {
    super.initState();
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    
    _bounceController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));
    
    _bounceAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _bounceController,
      curve: Curves.elasticOut,
    ));

    _startDemo();
  }

  void _startDemo() {
    Future.delayed(const Duration(milliseconds: 1000), () {
      if (mounted) {
        _pulseController.repeat(reverse: true);
        
        Future.delayed(const Duration(milliseconds: 2000), () {
          if (mounted) {
            setState(() {
              _showDetection = true;
              _detectedObjects = _getDetectedObjectsForPage();
            });
            _bounceController.forward();
          }
        });
      }
    });
  }

  List<Map<String, dynamic>> _getDetectedObjectsForPage() {
    switch (widget.pageIndex) {
      case 0: // Real-time detection
        return [
          {
            "label": "Phone",
            "confidence": 0.94,
            "left": 25.0,
            "top": 40.0,
            "width": 20.0,
            "height": 15.0,
          },
          {
            "label": "Cup",
            "confidence": 0.87,
            "left": 60.0,
            "top": 60.0,
            "width": 15.0,
            "height": 12.0,
          },
        ];
      case 1: // Offline capability
        return [
          {
            "label": "Laptop",
            "confidence": 0.91,
            "left": 30.0,
            "top": 35.0,
            "width": 40.0,
            "height": 25.0,
          },
        ];
      case 2: // Accessibility
        return [
          {
            "label": "Book",
            "confidence": 0.89,
            "left": 20.0,
            "top": 50.0,
            "width": 25.0,
            "height": 20.0,
          },
          {
            "label": "Glasses",
            "confidence": 0.82,
            "left": 55.0,
            "top": 30.0,
            "width": 18.0,
            "height": 8.0,
          },
        ];
      default:
        return [];
    }
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _bounceController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    return Stack(
      children: [
        // Scanning effect
        if (!_showDetection)
          AnimatedBuilder(
            animation: _pulseAnimation,
            builder: (context, child) {
              return Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: (isDark 
                        ? AppTheme.darkTheme.colorScheme.primary
                        : AppTheme.lightTheme.colorScheme.primary)
                        .withValues(alpha: 0.6),
                    width: 2,
                  ),
                ),
                child: Transform.scale(
                  scale: _pulseAnimation.value,
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(20),
                      color: (isDark 
                          ? AppTheme.darkTheme.colorScheme.primary
                          : AppTheme.lightTheme.colorScheme.primary)
                          .withValues(alpha: 0.1),
                    ),
                  ),
                ),
              );
            },
          ),
        
        // Detection overlays
        if (_showDetection)
          ..._detectedObjects.map((obj) => AnimatedBuilder(
                animation: _bounceAnimation,
                builder: (context, child) {
                  return Positioned(
                    left: (obj["left"] as double) * 0.01 * 80.w,
                    top: (obj["top"] as double) * 0.01 * 35.h,
                    child: Transform.scale(
                      scale: _bounceAnimation.value,
                      child: Container(
                        width: (obj["width"] as double) * 0.01 * 80.w,
                        height: (obj["height"] as double) * 0.01 * 35.h,
                        decoration: BoxDecoration(
                          border: Border.all(
                            color: _getConfidenceColor(obj["confidence"] as double, isDark),
                            width: 2,
                          ),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Stack(
                          children: [
                            // Label background
                            Positioned(
                              top: -8,
                              left: 0,
                              child: Container(
                                padding: EdgeInsets.symmetric(
                                  horizontal: 2.w,
                                  vertical: 0.5.h,
                                ),
                                decoration: BoxDecoration(
                                  color: _getConfidenceColor(obj["confidence"] as double, isDark),
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: Text(
                                  "${obj["label"]} ${(obj["confidence"] as double * 100).toInt()}%",
                                  style: Theme.of(context).textTheme.labelSmall?.copyWith(
                                    color: Colors.white,
                                    fontWeight: FontWeight.w600,
                                    fontSize: 8.sp,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                },
              )),
        
        // Tap instruction
        if (_showDetection)
          Positioned(
            bottom: 2.h,
            left: 0,
            right: 0,
            child: AnimatedBuilder(
              animation: _bounceAnimation,
              builder: (context, child) {
                return Opacity(
                  opacity: _bounceAnimation.value,
                  child: Container(
                    margin: EdgeInsets.symmetric(horizontal: 4.w),
                    padding: EdgeInsets.symmetric(
                      horizontal: 3.w,
                      vertical: 1.h,
                    ),
                    decoration: BoxDecoration(
                      color: (isDark 
                          ? AppTheme.darkTheme.colorScheme.surface
                          : AppTheme.lightTheme.colorScheme.surface)
                          .withValues(alpha: 0.9),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: (isDark 
                            ? AppTheme.darkTheme.colorScheme.outline
                            : AppTheme.lightTheme.colorScheme.outline)
                            .withValues(alpha: 0.3),
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        CustomIconWidget(
                          iconName: 'touch_app',
                          color: isDark 
                              ? AppTheme.darkTheme.colorScheme.primary
                              : AppTheme.lightTheme.colorScheme.primary,
                          size: 16,
                        ),
                        SizedBox(width: 2.w),
                        Text(
                          'Tap objects to see detection',
                          style: Theme.of(context).textTheme.labelSmall?.copyWith(
                            color: isDark 
                                ? AppTheme.darkTheme.colorScheme.onSurface
                                : AppTheme.lightTheme.colorScheme.onSurface,
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
      ],
    );
  }

  Color _getConfidenceColor(double confidence, bool isDark) {
    if (confidence >= 0.8) {
      return isDark ? AppTheme.darkTheme.colorScheme.tertiary : AppTheme.lightTheme.colorScheme.tertiary;
    } else if (confidence >= 0.6) {
      return AppTheme.getWarningColor(isDark);
    } else {
      return AppTheme.getErrorColor(isDark);
    }
  }
}