import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';
import '../../../theme/app_theme.dart';

class PageIndicatorWidget extends StatelessWidget {
  final int currentPage;
  final int totalPages;

  const PageIndicatorWidget({
    super.key,
    required this.currentPage,
    required this.totalPages,
  });

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(
        totalPages,
        (index) => AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
          margin: EdgeInsets.symmetric(horizontal: 1.w),
          width: index == currentPage ? 8.w : 2.w,
          height: 1.h,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            color: index == currentPage
                ? (isDark
                    ? AppTheme.darkTheme.colorScheme.primary
                    : AppTheme.lightTheme.colorScheme.primary)
                : (isDark
                        ? AppTheme.darkTheme.colorScheme.outline
                        : AppTheme.lightTheme.colorScheme.outline)
                    .withValues(alpha: 0.3),
          ),
        ),
      ),
    );
  }
}
