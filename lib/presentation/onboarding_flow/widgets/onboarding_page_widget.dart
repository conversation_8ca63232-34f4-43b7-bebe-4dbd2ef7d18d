import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';
import './feature_item_widget.dart';
import './interactive_demo_widget.dart';

class OnboardingPageWidget extends StatefulWidget {
  final Map<String, dynamic> data;
  final bool isActive;

  const OnboardingPageWidget({
    super.key,
    required this.data,
    required this.isActive,
  });

  @override
  State<OnboardingPageWidget> createState() => _OnboardingPageWidgetState();
}

class _OnboardingPageWidgetState extends State<OnboardingPageWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _slideAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _slideAnimation = Tween<double>(
      begin: 50.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutBack,
    ));
  }

  @override
  void didUpdateWidget(OnboardingPageWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isActive && !oldWidget.isActive) {
      _animationController.forward();
    } else if (!widget.isActive) {
      _animationController.reset();
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    if (widget.isActive) {
      _animationController.forward();
    }

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, _slideAnimation.value),
          child: Transform.scale(
            scale: _scaleAnimation.value,
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 6.w),
              child: Column(
                children: [
                  // Illustration section
                  Expanded(
                    flex: 5,
                    child: Container(
                      width: double.infinity,
                      margin: EdgeInsets.symmetric(vertical: 2.h),
                      child: Stack(
                        alignment: Alignment.center,
                        children: [
                          // Background gradient
                          Container(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(24),
                              gradient: LinearGradient(
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                                colors: [
                                  (isDark
                                          ? AppTheme
                                              .darkTheme.colorScheme.primary
                                          : AppTheme
                                              .lightTheme.colorScheme.primary)
                                      .withValues(alpha: 0.1),
                                  (isDark
                                          ? AppTheme
                                              .darkTheme.colorScheme.tertiary
                                          : AppTheme
                                              .lightTheme.colorScheme.tertiary)
                                      .withValues(alpha: 0.05),
                                ],
                              ),
                            ),
                          ),

                          // Main illustration
                          ClipRRect(
                            borderRadius: BorderRadius.circular(20),
                            child: CustomImageWidget(
                              imageUrl: widget.data["illustration"] as String,
                              width: 80.w,
                              height: 35.h,
                              fit: BoxFit.cover,
                            ),
                          ),

                          // Interactive demo overlay
                          if (widget.isActive)
                            Positioned.fill(
                              child: InteractiveDemoWidget(
                                pageIndex: widget.data["title"] ==
                                        "Real-Time Object Detection"
                                    ? 0
                                    : widget.data["title"] ==
                                            "Works Completely Offline"
                                        ? 1
                                        : 2,
                              ),
                            ),
                        ],
                      ),
                    ),
                  ),

                  // Content section
                  Expanded(
                    flex: 4,
                    child: Column(
                      children: [
                        // Title
                        Text(
                          widget.data["title"] as String,
                          style: Theme.of(context)
                              .textTheme
                              .headlineSmall
                              ?.copyWith(
                                fontWeight: FontWeight.w700,
                                color: isDark
                                    ? AppTheme.darkTheme.colorScheme.onSurface
                                    : AppTheme.lightTheme.colorScheme.onSurface,
                              ),
                          textAlign: TextAlign.center,
                        ),

                        SizedBox(height: 2.h),

                        // Description
                        Text(
                          widget.data["description"] as String,
                          style:
                              Theme.of(context).textTheme.bodyLarge?.copyWith(
                                    color: isDark
                                        ? AppTheme.darkTheme.colorScheme
                                            .onSurfaceVariant
                                        : AppTheme.lightTheme.colorScheme
                                            .onSurfaceVariant,
                                    height: 1.5,
                                  ),
                          textAlign: TextAlign.center,
                        ),

                        SizedBox(height: 3.h),

                        // Features list
                        Column(
                          children: (widget.data["features"] as List)
                              .map((feature) => FeatureItemWidget(
                                    icon: feature["icon"] as String,
                                    text: feature["text"] as String,
                                    delay: (widget.data["features"] as List)
                                            .indexOf(feature) *
                                        100,
                                    isActive: widget.isActive,
                                  ))
                              .toList(),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
