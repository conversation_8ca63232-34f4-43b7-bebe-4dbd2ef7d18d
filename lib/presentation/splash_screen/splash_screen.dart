import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:sizer/sizer.dart';

import '../../core/app_export.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with TickerProviderStateMixin {
  late AnimationController _logoAnimationController;
  late AnimationController _progressAnimationController;
  late Animation<double> _logoScaleAnimation;
  late Animation<double> _logoOpacityAnimation;
  late Animation<double> _progressAnimation;

  String _loadingText = 'Loading AI Models...';
  double _progress = 0.0;
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _initializeApp();
  }

  void _setupAnimations() {
    _logoAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _progressAnimationController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _logoScaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _logoAnimationController,
      curve: Curves.elasticOut,
    ));

    _logoOpacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _logoAnimationController,
      curve: Curves.easeInOut,
    ));

    _progressAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _progressAnimationController,
      curve: Curves.easeInOut,
    ));

    _logoAnimationController.forward();
    _progressAnimationController.forward();
  }

  Future<void> _initializeApp() async {
    try {
      // Simulate ML model loading phases
      await _loadTensorFlowLiteModel();
      await _requestCameraPermissions();
      await _checkDeviceCapabilities();
      await _prepareDetectionClasses();

      setState(() {
        _isInitialized = true;
      });

      // Navigate after successful initialization
      await Future.delayed(const Duration(milliseconds: 500));
      _navigateToNextScreen();
    } catch (e) {
      _handleInitializationError();
    }
  }

  Future<void> _loadTensorFlowLiteModel() async {
    setState(() {
      _loadingText = 'Loading TensorFlow Lite Model...';
      _progress = 0.25;
    });
    await Future.delayed(const Duration(milliseconds: 800));
  }

  Future<void> _requestCameraPermissions() async {
    setState(() {
      _loadingText = 'Requesting Camera Permissions...';
      _progress = 0.5;
    });
    await Future.delayed(const Duration(milliseconds: 600));
  }

  Future<void> _checkDeviceCapabilities() async {
    setState(() {
      _loadingText = 'Checking Device Capabilities...';
      _progress = 0.75;
    });
    await Future.delayed(const Duration(milliseconds: 500));
  }

  Future<void> _prepareDetectionClasses() async {
    setState(() {
      _loadingText = 'Preparing Detection Classes...';
      _progress = 1.0;
    });
    await Future.delayed(const Duration(milliseconds: 400));
  }

  void _navigateToNextScreen() {
    // Mock permission and onboarding status
    final bool hasPermissions = true; // Mock granted permissions
    final bool isFirstTime = false; // Mock returning user

    if (!hasPermissions) {
      Navigator.pushReplacementNamed(context, '/permission-request-screen');
    } else if (isFirstTime) {
      Navigator.pushReplacementNamed(context, '/onboarding-flow');
    } else {
      Navigator.pushReplacementNamed(context, '/camera-detection-screen');
    }
  }

  void _handleInitializationError() {
    setState(() {
      _loadingText = 'Initialization failed. Retrying...';
    });

    Future.delayed(const Duration(seconds: 5), () {
      _initializeApp();
    });
  }

  @override
  void dispose() {
    _logoAnimationController.dispose();
    _progressAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final bool isDark = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      body: AnnotatedRegion<SystemUiOverlayStyle>(
        value: SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: isDark ? Brightness.light : Brightness.dark,
          systemNavigationBarColor:
              isDark ? AppTheme.backgroundDark : AppTheme.backgroundLight,
          systemNavigationBarIconBrightness:
              isDark ? Brightness.light : Brightness.dark,
        ),
        child: Container(
          width: 100.w,
          height: 100.h,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: isDark
                  ? [
                      AppTheme.backgroundDark,
                      AppTheme.surfaceDark,
                      AppTheme.primaryDark.withValues(alpha: 0.1),
                    ]
                  : [
                      AppTheme.backgroundLight,
                      AppTheme.surfaceLight,
                      AppTheme.primaryLight.withValues(alpha: 0.1),
                    ],
            ),
          ),
          child: SafeArea(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Logo Section
                Expanded(
                  flex: 3,
                  child: Center(
                    child: AnimatedBuilder(
                      animation: _logoAnimationController,
                      builder: (context, child) {
                        return Transform.scale(
                          scale: _logoScaleAnimation.value,
                          child: Opacity(
                            opacity: _logoOpacityAnimation.value,
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                // App Logo with AI/Camera iconography
                                Container(
                                  width: 25.w,
                                  height: 25.w,
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    gradient: LinearGradient(
                                      begin: Alignment.topLeft,
                                      end: Alignment.bottomRight,
                                      colors: [
                                        isDark
                                            ? AppTheme.primaryDark
                                            : AppTheme.primaryLight,
                                        isDark
                                            ? AppTheme.accentDark
                                            : AppTheme.accentLight,
                                      ],
                                    ),
                                    boxShadow: [
                                      BoxShadow(
                                        color: (isDark
                                                ? AppTheme.primaryDark
                                                : AppTheme.primaryLight)
                                            .withValues(alpha: 0.3),
                                        blurRadius: 20,
                                        offset: const Offset(0, 10),
                                      ),
                                    ],
                                  ),
                                  child: Center(
                                    child: CustomIconWidget(
                                      iconName: 'camera_alt',
                                      color:
                                          isDark ? Colors.black : Colors.white,
                                      size: 12.w,
                                    ),
                                  ),
                                ),
                                SizedBox(height: 3.h),
                                // App Name
                                Text(
                                  'Visionary',
                                  style: Theme.of(context)
                                      .textTheme
                                      .headlineLarge
                                      ?.copyWith(
                                        fontWeight: FontWeight.w700,
                                        color: isDark
                                            ? AppTheme.textPrimaryDark
                                            : AppTheme.textPrimaryLight,
                                      ),
                                ),
                                SizedBox(height: 1.h),
                                // Tagline
                                Text(
                                  'AI-Powered Object Detection',
                                  style: Theme.of(context)
                                      .textTheme
                                      .bodyMedium
                                      ?.copyWith(
                                        color: isDark
                                            ? AppTheme.textSecondaryDark
                                            : AppTheme.textSecondaryLight,
                                      ),
                                ),
                              ],
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ),

                // Loading Section
                Expanded(
                  flex: 1,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // Progress Indicator
                      Container(
                        width: 60.w,
                        height: 0.6.h,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(3),
                          color: (isDark
                                  ? AppTheme.primaryDark
                                  : AppTheme.primaryLight)
                              .withValues(alpha: 0.2),
                        ),
                        child: AnimatedBuilder(
                          animation: _progressAnimation,
                          builder: (context, child) {
                            return Stack(
                              children: [
                                Container(
                                  width: 60.w * _progress,
                                  height: 0.6.h,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(3),
                                    gradient: LinearGradient(
                                      colors: [
                                        isDark
                                            ? AppTheme.primaryDark
                                            : AppTheme.primaryLight,
                                        isDark
                                            ? AppTheme.accentDark
                                            : AppTheme.accentLight,
                                      ],
                                    ),
                                  ),
                                ),
                              ],
                            );
                          },
                        ),
                      ),
                      SizedBox(height: 2.h),

                      // Loading Text
                      Text(
                        _loadingText,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: isDark
                                  ? AppTheme.textSecondaryDark
                                  : AppTheme.textSecondaryLight,
                            ),
                      ),
                      SizedBox(height: 1.h),

                      // Progress Percentage
                      Text(
                        '${(_progress * 100).toInt()}%',
                        style:
                            Theme.of(context).textTheme.labelMedium?.copyWith(
                                  color: isDark
                                      ? AppTheme.primaryDark
                                      : AppTheme.primaryLight,
                                  fontWeight: FontWeight.w600,
                                ),
                      ),
                    ],
                  ),
                ),

                // Bottom Section
                Expanded(
                  flex: 1,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      // Compatibility Info
                      if (!_isInitialized)
                        Padding(
                          padding: EdgeInsets.symmetric(horizontal: 8.w),
                          child: Text(
                            'Optimizing for your device...',
                            textAlign: TextAlign.center,
                            style:
                                Theme.of(context).textTheme.bodySmall?.copyWith(
                                      color: isDark
                                          ? AppTheme.textSecondaryDark
                                          : AppTheme.textSecondaryLight,
                                    ),
                          ),
                        ),
                      SizedBox(height: 4.h),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
