import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:sizer/sizer.dart';

import '../../core/app_export.dart';
import '../../core/ml/detection_model.dart';
import '../../core/services/camera_service.dart';
import '../../core/services/detection_service.dart';
import '../../core/services/storage_service.dart';
import '../../core/services/performance_service.dart';
import './widgets/camera_controls_widget.dart';
import './widgets/camera_preview_widget.dart';
import './widgets/detection_history_sheet.dart';
import './widgets/detection_overlay_widget.dart';
import './widgets/detection_stats_widget.dart';

class CameraDetectionScreen extends StatefulWidget {
  const CameraDetectionScreen({super.key});

  @override
  State<CameraDetectionScreen> createState() => _CameraDetectionScreenState();
}

class _CameraDetectionScreenState extends State<CameraDetectionScreen>
    with TickerProviderStateMixin, WidgetsBindingObserver {

  // Services
  late CameraService _cameraService;
  late DetectionModel _detectionModel;
  late DetectionService _detectionService;
  late StorageService _storageService;
  late PerformanceService _performanceService;

  // UI State
  bool _showHistorySheet = false;
  bool _isInitializing = true;
  String? _initializationError;

  // Animation controllers
  late AnimationController _captureAnimationController;
  late AnimationController _detectionAnimationController;

  // Detection history from storage
  List<Map<String, dynamic>> _detectionHistory = [];

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);

    // Initialize animation controllers
    _captureAnimationController = AnimationController(
      duration: Duration(milliseconds: 200),
      vsync: this,
    );
    _detectionAnimationController = AnimationController(
      duration: Duration(milliseconds: 1500),
      vsync: this,
    )..repeat();

    // Initialize services
    _initializeServices();

    // Load detection history
    _loadDetectionHistory();
  }

  /// Initialize all required services
  Future<void> _initializeServices() async {
    try {
      setState(() {
        _isInitializing = true;
        _initializationError = null;
      });

      // Initialize performance service (singleton)
      _performanceService = PerformanceService();

      // Initialize storage service
      _storageService = StorageService();
      final storageInitialized = await _storageService.initialize();
      if (!storageInitialized) {
        throw Exception('Failed to initialize storage service');
      }

      // Initialize services
      _cameraService = CameraService();
      _detectionModel = DetectionModel();
      _detectionService = DetectionService(_cameraService, _detectionModel, _performanceService);

      // Initialize detection service
      final detectionInitialized = await _detectionService.initialize();
      if (!detectionInitialized) {
        throw Exception(_detectionService.error ?? 'Failed to initialize detection service');
      }

      // Listen to detection service changes
      _detectionService.addListener(_onDetectionServiceChanged);

      // Start detection
      _detectionService.startDetection();

      setState(() {
        _isInitializing = false;
      });

      print('All services initialized successfully');
    } catch (e) {
      setState(() {
        _isInitializing = false;
        _initializationError = e.toString();
      });
      print('Error initializing services: $e');
    }
  }

  /// Handle detection service state changes
  void _onDetectionServiceChanged() {
    if (mounted) {
      setState(() {});
    }
  }

  /// Load detection history from storage
  Future<void> _loadDetectionHistory() async {
    try {
      final history = await StorageService.getSnapshots(limit: 10);
      setState(() {
        _detectionHistory = history;
      });
    } catch (e) {
      print('Error loading detection history: $e');
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _detectionService.removeListener(_onDetectionServiceChanged);
    _detectionService.dispose();
    _captureAnimationController.dispose();
    _detectionAnimationController.dispose();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    if (!_isInitializing && _initializationError == null) {
      switch (state) {
        case AppLifecycleState.paused:
          _detectionService.pause();
          break;
        case AppLifecycleState.resumed:
          _detectionService.resume();
          break;
        default:
          break;
      }
    }
  }

  /// Toggle flash
  Future<void> _toggleFlash() async {
    await _detectionService.toggleFlash();
    HapticFeedback.lightImpact();
  }

  /// Switch camera
  Future<void> _toggleCamera() async {
    await _detectionService.switchCamera();
    HapticFeedback.mediumImpact();
  }

  /// Capture snapshot with current detections
  Future<void> _captureSnapshot() async {
    _captureAnimationController.forward().then((_) {
      _captureAnimationController.reverse();
    });
    HapticFeedback.heavyImpact();

    try {
      final snapshot = await _detectionService.captureSnapshot();
      if (snapshot != null) {
        // Save to storage
        final saved = await StorageService.saveSnapshot(snapshot);
        if (saved) {
          // Reload history
          await _loadDetectionHistory();

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Snapshot captured with ${snapshot['totalDetections']} detections',
              ),
              duration: Duration(seconds: 2),
            ),
          );
        } else {
          throw Exception('Failed to save snapshot');
        }
      } else {
        throw Exception('Failed to capture snapshot');
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error capturing snapshot: $e'),
          backgroundColor: Colors.red,
          duration: Duration(seconds: 3),
        ),
      );
    }
  }

  /// Handle detection tap for voice feedback
  void _onDetectionTap(Detection detection) {
    HapticFeedback.lightImpact();
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          '${detection.label} detected with ${(detection.confidence * 100).toInt()}% confidence',
        ),
        duration: Duration(seconds: 2),
      ),
    );
  }

  /// Handle detection long press for detailed info
  void _onDetectionLongPress(Detection detection) {
    HapticFeedback.heavyImpact();
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Detection Details'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Object: ${detection.label}'),
            Text('Confidence: ${(detection.confidence * 100).toStringAsFixed(1)}%'),
            Text('Position: (${detection.x.toInt()}, ${detection.y.toInt()})'),
            Text('Size: ${detection.width.toInt()} × ${detection.height.toInt()}'),
            Text('Time: ${detection.timestamp.toString().split('.')[0]}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Close'),
          ),
        ],
      ),
    );
  }

  /// Handle pinch to zoom
  Future<void> _onPinchToZoom(double zoom) async {
    await _detectionService.setZoomLevel(zoom);
  }

  /// Toggle history sheet
  void _toggleHistorySheet() {
    setState(() {
      _showHistorySheet = !_showHistorySheet;
    });
  }

  /// Navigate to history screen
  void _navigateToHistory() {
    Navigator.pushNamed(context, '/detection-history-screen');
  }

  /// Navigate to settings
  void _navigateToSettings() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Settings screen would open here')),
    );
  }

  @override
  Widget build(BuildContext context) {
    // Show initialization screen
    if (_isInitializing) {
      return _buildInitializationScreen();
    }

    // Show error screen
    if (_initializationError != null) {
      return _buildErrorScreen();
    }

    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          // Camera Preview (Full Screen)
          CameraPreviewWidget(
            cameraService: _cameraService,
            onPinchToZoom: _onPinchToZoom,
          ),

          // Detection Overlays
          DetectionOverlayWidget(
            detections: _detectionService.currentDetections,
            onDetectionTap: _onDetectionTap,
            onDetectionLongPress: _onDetectionLongPress,
            animationController: _detectionAnimationController,
          ),

          // Top Status Bar
          SafeArea(
            child: Container(
              width: double.infinity,
              padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.h),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.black.withValues(alpha: 0.6),
                    Colors.transparent,
                  ],
                ),
              ),
              child: DetectionStatsWidget(
                detectionCount: _detectionService.currentDetections.length,
                processingFps: _detectionService.processingFps,
                isDetecting: _detectionService.isDetecting,
                totalDetections: _detectionService.totalDetections,
              ),
            ),
          ),

          // Camera Controls
          Positioned(
            top: 8.h,
            left: 4.w,
            child: CameraControlButton(
              icon: _cameraService.isFlashOn ? 'flash_on' : 'flash_off',
              onPressed: _toggleFlash,
              isActive: _cameraService.isFlashOn,
            ),
          ),

          Positioned(
            top: 8.h,
            right: 4.w,
            child: CameraControlButton(
              icon: 'flip_camera_ios',
              onPressed: _toggleCamera,
              isActive: false,
            ),
          ),

          // Bottom Controls
          Positioned(
            bottom: 8.h,
            left: 0,
            right: 0,
            child: CameraControlsWidget(
              onCapture: _captureSnapshot,
              onHistoryToggle: _toggleHistorySheet,
              onNavigateToHistory: _navigateToHistory,
              captureAnimation: _captureAnimationController,
              detectionCount: _detectionService.currentDetections.length,
            ),
          ),

          // Detection History Bottom Sheet
          if (_showHistorySheet)
            DetectionHistorySheet(
              detectionHistory: _detectionHistory,
              onClose: _toggleHistorySheet,
              onNavigateToDetail: (detection) {
                Navigator.pushNamed(context, '/detection-detail-screen');
              },
            ),
        ],
      ),
    );
  }

  /// Build initialization screen
  Widget _buildInitializationScreen() {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              color: AppTheme.primaryLight,
            ),
            SizedBox(height: 3.h),
            Text(
              'Initializing Camera & AI Model...',
              style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                color: Colors.white,
              ),
            ),
            SizedBox(height: 1.h),
            Text(
              'Please wait while we set up the detection system',
              style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                color: Colors.white.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// Build error screen
  Widget _buildErrorScreen() {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Center(
        child: Padding(
          padding: EdgeInsets.all(4.w),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CustomIconWidget(
                iconName: 'error',
                color: Colors.red,
                size: 80,
              ),
              SizedBox(height: 3.h),
              Text(
                'Initialization Failed',
                style: AppTheme.lightTheme.textTheme.titleLarge?.copyWith(
                  color: Colors.white,
                ),
              ),
              SizedBox(height: 2.h),
              Text(
                _initializationError ?? 'Unknown error occurred',
                style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                  color: Colors.white.withValues(alpha: 0.8),
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 4.h),
              ElevatedButton(
                onPressed: () {
                  setState(() {
                    _initializationError = null;
                  });
                  _initializeServices();
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.primaryLight,
                  padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 2.h),
                ),
                child: Text(
                  'Retry',
                  style: TextStyle(color: Colors.white),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class CameraControlButton extends StatelessWidget {
  final String icon;
  final VoidCallback onPressed;
  final bool isActive;

  const CameraControlButton({
    super.key,
    required this.icon,
    required this.onPressed,
    required this.isActive,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 12.w,
      height: 6.h,
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.6),
        borderRadius: BorderRadius.circular(8),
        border: isActive
            ? Border.all(
                color: AppTheme.lightTheme.primaryColor,
                width: 2,
              )
            : null,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(8),
          child: Center(
            child: CustomIconWidget(
              iconName: icon,
              color: isActive ? AppTheme.lightTheme.primaryColor : Colors.white,
              size: 24,
            ),
          ),
        ),
      ),
    );
  }
}
