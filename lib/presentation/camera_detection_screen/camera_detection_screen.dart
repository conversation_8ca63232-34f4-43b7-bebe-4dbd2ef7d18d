import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:sizer/sizer.dart';

import '../../core/app_export.dart';
import './widgets/camera_controls_widget.dart';
import './widgets/camera_preview_widget.dart';
import './widgets/detection_history_sheet.dart';
import './widgets/detection_overlay_widget.dart';
import './widgets/detection_stats_widget.dart';

class CameraDetectionScreen extends StatefulWidget {
  const CameraDetectionScreen({super.key});

  @override
  State<CameraDetectionScreen> createState() => _CameraDetectionScreenState();
}

class _CameraDetectionScreenState extends State<CameraDetectionScreen>
    with TickerProviderStateMixin {
  bool _isFlashOn = false;
  bool _isRearCamera = true;
  final bool _isDetecting = true;
  bool _isOffline = false;
  int _detectionCount = 0;
  final double _modelPerformance = 85.6;
  double _zoomLevel = 1.0;
  bool _showHistorySheet = false;

  late AnimationController _captureAnimationController;
  late AnimationController _detectionAnimationController;

  // Mock detection data
  final List<Map<String, dynamic>> _currentDetections = [
    {
      "id": 1,
      "label": "Person",
      "confidence": 0.94,
      "boundingBox": {"x": 120.0, "y": 200.0, "width": 180.0, "height": 320.0},
      "timestamp": DateTime.now(),
    },
    {
      "id": 2,
      "label": "Mobile Phone",
      "confidence": 0.87,
      "boundingBox": {"x": 250.0, "y": 150.0, "width": 80.0, "height": 140.0},
      "timestamp": DateTime.now(),
    },
    {
      "id": 3,
      "label": "Chair",
      "confidence": 0.76,
      "boundingBox": {"x": 50.0, "y": 400.0, "width": 120.0, "height": 160.0},
      "timestamp": DateTime.now(),
    },
  ];

  final List<Map<String, dynamic>> _detectionHistory = [
    {
      "id": 1,
      "image":
          "https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=400&h=300&fit=crop",
      "detections": ["Person", "Mobile Phone", "Chair"],
      "timestamp": DateTime.now().subtract(Duration(minutes: 5)),
      "confidence": 0.89,
    },
    {
      "id": 2,
      "image":
          "https://images.pexels.com/photos/1181467/pexels-photo-1181467.jpeg?w=400&h=300&fit=crop",
      "detections": ["Car", "Traffic Light"],
      "timestamp": DateTime.now().subtract(Duration(minutes: 15)),
      "confidence": 0.92,
    },
    {
      "id": 3,
      "image":
          "https://images.pixabay.com/photos/2017/07/31/11/21/people-2557396_1280.jpg?w=400&h=300&fit=crop",
      "detections": ["Person", "Dog", "Bicycle"],
      "timestamp": DateTime.now().subtract(Duration(hours: 1)),
      "confidence": 0.85,
    },
  ];

  @override
  void initState() {
    super.initState();
    _captureAnimationController = AnimationController(
      duration: Duration(milliseconds: 200),
      vsync: this,
    );
    _detectionAnimationController = AnimationController(
      duration: Duration(milliseconds: 1500),
      vsync: this,
    )..repeat();

    _updateDetectionCount();
    _checkConnectivity();
  }

  @override
  void dispose() {
    _captureAnimationController.dispose();
    _detectionAnimationController.dispose();
    super.dispose();
  }

  void _updateDetectionCount() {
    setState(() {
      _detectionCount = _currentDetections.length;
    });
  }

  void _checkConnectivity() {
    // Mock connectivity check
    setState(() {
      _isOffline = false; // Simulating online status
    });
  }

  void _toggleFlash() {
    setState(() {
      _isFlashOn = !_isFlashOn;
    });
    HapticFeedback.lightImpact();
  }

  void _toggleCamera() {
    setState(() {
      _isRearCamera = !_isRearCamera;
    });
    HapticFeedback.mediumImpact();
  }

  void _captureSnapshot() {
    _captureAnimationController.forward().then((_) {
      _captureAnimationController.reverse();
    });
    HapticFeedback.heavyImpact();

    // Add to history
    final newDetection = {
      "id": _detectionHistory.length + 1,
      "image":
          "https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=400&h=300&fit=crop",
      "detections":
          _currentDetections.map((d) => d["label"] as String).toList(),
      "timestamp": DateTime.now(),
      "confidence": _currentDetections.isNotEmpty
          ? _currentDetections
                  .map((d) => d["confidence"] as double)
                  .reduce((a, b) => a + b) /
              _currentDetections.length
          : 0.0,
    };

    setState(() {
      _detectionHistory.insert(0, newDetection);
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
            'Snapshot captured with ${_currentDetections.length} detections'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  void _onDetectionLongPress(Map<String, dynamic> detection) {
    HapticFeedback.heavyImpact();
    // Voice readout simulation
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
            'Voice: ${detection["label"]} detected with ${(detection["confidence"] * 100).toInt()}% confidence'),
        duration: Duration(seconds: 3),
      ),
    );
  }

  void _onPinchToZoom(double scale) {
    setState(() {
      _zoomLevel = (scale * 1.0).clamp(1.0, 5.0);
    });
  }

  void _toggleHistorySheet() {
    setState(() {
      _showHistorySheet = !_showHistorySheet;
    });
  }

  void _navigateToHistory() {
    Navigator.pushNamed(context, '/detection-history-screen');
  }

  void _navigateToSettings() {
    // Mock settings navigation
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Settings screen would open here')),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: AppTheme.lightTheme.scaffoldBackgroundColor,
      body: Stack(
        children: [
          // Camera Preview (Full Screen)
          CameraPreviewWidget(
            isRearCamera: _isRearCamera,
            isFlashOn: _isFlashOn,
            zoomLevel: _zoomLevel,
            onPinchToZoom: _onPinchToZoom,
          ),

          // Detection Overlays
          DetectionOverlayWidget(
            detections: _currentDetections,
            onDetectionLongPress: _onDetectionLongPress,
            animationController: _detectionAnimationController,
          ),

          // Top Status Bar
          SafeArea(
            child: Container(
              width: double.infinity,
              padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.h),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.black.withValues(alpha: 0.6),
                    Colors.transparent,
                  ],
                ),
              ),
              child: DetectionStatsWidget(
                detectionCount: _detectionCount,
                modelPerformance: _modelPerformance,
                isOffline: _isOffline,
                isDetecting: _isDetecting,
              ),
            ),
          ),

          // Camera Controls
          Positioned(
            top: 8.h,
            left: 4.w,
            child: CameraControlButton(
              icon: _isFlashOn ? 'flash_on' : 'flash_off',
              onPressed: _toggleFlash,
              isActive: _isFlashOn,
            ),
          ),

          Positioned(
            top: 8.h,
            right: 4.w,
            child: CameraControlButton(
              icon: 'flip_camera_ios',
              onPressed: _toggleCamera,
              isActive: false,
            ),
          ),

          // Bottom Controls
          Positioned(
            bottom: 8.h,
            left: 0,
            right: 0,
            child: CameraControlsWidget(
              onCapture: _captureSnapshot,
              onHistoryToggle: _toggleHistorySheet,
              onNavigateToHistory: _navigateToHistory,
              captureAnimation: _captureAnimationController,
              detectionCount: _detectionCount,
            ),
          ),

          // Detection History Bottom Sheet
          if (_showHistorySheet)
            DetectionHistorySheet(
              detectionHistory: _detectionHistory,
              onClose: _toggleHistorySheet,
              onNavigateToDetail: (detection) {
                Navigator.pushNamed(context, '/detection-detail-screen');
              },
            ),
        ],
      ),
    );
  }
}

class CameraControlButton extends StatelessWidget {
  final String icon;
  final VoidCallback onPressed;
  final bool isActive;

  const CameraControlButton({
    super.key,
    required this.icon,
    required this.onPressed,
    required this.isActive,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 12.w,
      height: 6.h,
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.6),
        borderRadius: BorderRadius.circular(8),
        border: isActive
            ? Border.all(
                color: AppTheme.lightTheme.primaryColor,
                width: 2,
              )
            : null,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(8),
          child: Center(
            child: CustomIconWidget(
              iconName: icon,
              color: isActive ? AppTheme.lightTheme.primaryColor : Colors.white,
              size: 24,
            ),
          ),
        ),
      ),
    );
  }
}
