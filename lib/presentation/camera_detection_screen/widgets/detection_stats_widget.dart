import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

class DetectionStatsWidget extends StatelessWidget {
  final int detectionCount;
  final double modelPerformance;
  final bool isOffline;
  final bool isDetecting;

  const DetectionStatsWidget({
    super.key,
    required this.detectionCount,
    required this.modelPerformance,
    required this.isOffline,
    required this.isDetecting,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        // Detection Status
        Container(
          padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 1.h),
          decoration: BoxDecoration(
            color: Colors.black.withValues(alpha: 0.7),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: isDetecting
                  ? AppTheme.getSuccessColor(false).withValues(alpha: 0.6)
                  : Colors.grey.withValues(alpha: 0.6),
              width: 1,
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 8,
                height: 8,
                decoration: BoxDecoration(
                  color: isDetecting
                      ? AppTheme.getSuccessColor(false)
                      : Colors.grey,
                  shape: BoxShape.circle,
                ),
              ),
              SizedBox(width: 2.w),
              Text(
                isDetecting ? 'Detecting' : 'Paused',
                style: AppTheme.lightTheme.textTheme.labelMedium?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),

        // Detection Count and Performance
        Container(
          padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 1.h),
          decoration: BoxDecoration(
            color: Colors.black.withValues(alpha: 0.7),
            borderRadius: BorderRadius.circular(20),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              CustomIconWidget(
                iconName: 'visibility',
                color: Colors.white,
                size: 16,
              ),
              SizedBox(width: 1.w),
              Text(
                detectionCount.toString(),
                style: AppTheme.lightTheme.textTheme.labelMedium?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
              SizedBox(width: 2.w),
              Container(
                width: 1,
                height: 16,
                color: Colors.white.withValues(alpha: 0.3),
              ),
              SizedBox(width: 2.w),
              CustomIconWidget(
                iconName: 'speed',
                color: Colors.white,
                size: 16,
              ),
              SizedBox(width: 1.w),
              Text(
                '${modelPerformance.toStringAsFixed(1)}%',
                style: AppTheme.lightTheme.textTheme.labelMedium?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),

        // Connection Status
        if (isOffline)
          Container(
            padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 1.h),
            decoration: BoxDecoration(
              color: Colors.black.withValues(alpha: 0.7),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: AppTheme.getWarningColor(false).withValues(alpha: 0.6),
                width: 1,
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                CustomIconWidget(
                  iconName: 'wifi_off',
                  color: AppTheme.getWarningColor(false),
                  size: 16,
                ),
                SizedBox(width: 2.w),
                Text(
                  'Offline',
                  style: AppTheme.lightTheme.textTheme.labelMedium?.copyWith(
                    color: AppTheme.getWarningColor(false),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          )
        else
          Container(
            padding: EdgeInsets.all(1.h),
            decoration: BoxDecoration(
              color: Colors.black.withValues(alpha: 0.7),
              borderRadius: BorderRadius.circular(20),
            ),
            child: CustomIconWidget(
              iconName: 'wifi',
              color: AppTheme.getSuccessColor(false),
              size: 16,
            ),
          ),
      ],
    );
  }
}
