import 'package:flutter/material.dart';
import '../../../core/services/detection_service.dart';
import '../../../core/services/performance_service.dart';
import '../../../core/app_export.dart';

/// Widget for displaying real-time performance metrics
class PerformanceMonitorWidget extends StatefulWidget {
  final DetectionService detectionService;
  final PerformanceService performanceService;
  final bool isVisible;

  const PerformanceMonitorWidget({
    Key? key,
    required this.detectionService,
    required this.performanceService,
    this.isVisible = false,
  }) : super(key: key);

  @override
  State<PerformanceMonitorWidget> createState() => _PerformanceMonitorWidgetState();
}

class _PerformanceMonitorWidgetState extends State<PerformanceMonitorWidget> {
  @override
  Widget build(BuildContext context) {
    if (!widget.isVisible) return const SizedBox.shrink();

    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    return Positioned(
      top: 100,
      right: 16,
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: (isDark ? Colors.black : Colors.white).withOpacity(0.8),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: (isDark ? Colors.white : Colors.black).withOpacity(0.2),
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Performance Monitor',
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.bold,
                color: isDark ? Colors.white : Colors.black,
              ),
            ),
            const SizedBox(height: 8),
            _buildPerformanceMetrics(isDark),
          ],
        ),
      ),
    );
  }

  Widget _buildPerformanceMetrics(bool isDark) {
    return ListenableBuilder(
      listenable: Listenable.merge([
        widget.detectionService,
        widget.performanceService,
      ]),
      builder: (context, child) {
        final stats = widget.detectionService.getPerformanceStats();
        
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildMetricRow(
              'FPS',
              '${stats['processingFps']?.toStringAsFixed(1) ?? '0.0'}',
              _getFpsColor(stats['processingFps']?.toDouble() ?? 0.0),
              isDark,
            ),
            _buildMetricRow(
              'Inference',
              '${stats['averageInferenceTime']?.toStringAsFixed(0) ?? '0'}ms',
              _getInferenceColor(stats['averageInferenceTime']?.toDouble() ?? 0.0),
              isDark,
            ),
            _buildMetricRow(
              'Memory',
              '${stats['memoryUsageMB'] ?? 0}MB',
              _getMemoryColor(stats['memoryUsageMB']?.toInt() ?? 0),
              isDark,
            ),
            _buildMetricRow(
              'CPU',
              '${stats['cpuUsagePercent']?.toStringAsFixed(0) ?? '0'}%',
              _getCpuColor(stats['cpuUsagePercent']?.toDouble() ?? 0.0),
              isDark,
            ),
            _buildMetricRow(
              'Queue',
              '${stats['queueSize'] ?? 0}/${stats['maxQueueSize'] ?? 0}',
              _getQueueColor(
                stats['queueSize']?.toInt() ?? 0,
                stats['maxQueueSize']?.toInt() ?? 1,
              ),
              isDark,
            ),
            _buildMetricRow(
              'Image Size',
              '${stats['recommendedImageSize'] ?? 0}px',
              Colors.blue,
              isDark,
            ),
            if (stats['isMemoryPressure'] == true)
              _buildWarningIndicator('Memory Pressure', Colors.orange, isDark),
            if (stats['isThermalThrottling'] == true)
              _buildWarningIndicator('Thermal Throttling', Colors.red, isDark),
            if (stats['isQueueFull'] == true)
              _buildWarningIndicator('Queue Full', Colors.amber, isDark),
          ],
        );
      },
    );
  }

  Widget _buildMetricRow(String label, String value, Color color, bool isDark) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            width: 70,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 10,
                color: (isDark ? Colors.white : Colors.black).withOpacity(0.8),
              ),
            ),
          ),
          Container(
            width: 4,
            height: 4,
            decoration: BoxDecoration(
              color: color,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 6),
          Text(
            value,
            style: TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.w600,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWarningIndicator(String warning, Color color, bool isDark) {
    return Padding(
      padding: const EdgeInsets.only(top: 4),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.warning,
            size: 12,
            color: color,
          ),
          const SizedBox(width: 4),
          Text(
            warning,
            style: TextStyle(
              fontSize: 9,
              fontWeight: FontWeight.w600,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Color _getFpsColor(double fps) {
    if (fps >= widget.performanceService.targetFps * 0.8) return Colors.green;
    if (fps >= widget.performanceService.targetFps * 0.6) return Colors.orange;
    return Colors.red;
  }

  Color _getInferenceColor(double inferenceTime) {
    if (inferenceTime <= 100) return Colors.green;
    if (inferenceTime <= 200) return Colors.orange;
    return Colors.red;
  }

  Color _getMemoryColor(int memoryMB) {
    if (memoryMB <= 100) return Colors.green;
    if (memoryMB <= 150) return Colors.orange;
    return Colors.red;
  }

  Color _getCpuColor(double cpuPercent) {
    if (cpuPercent <= 60) return Colors.green;
    if (cpuPercent <= 80) return Colors.orange;
    return Colors.red;
  }

  Color _getQueueColor(int current, int max) {
    final ratio = current / max;
    if (ratio <= 0.6) return Colors.green;
    if (ratio <= 0.8) return Colors.orange;
    return Colors.red;
  }
}
