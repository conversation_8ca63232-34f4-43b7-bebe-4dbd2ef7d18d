import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';
import '../../../core/ml/detection_model.dart';
import '../../../theme/app_theme.dart';

class DetectionOverlayWidget extends StatelessWidget {
  final List<Detection> detections;
  final Function(Detection)? onDetectionTap;
  final Function(Detection) onDetectionLongPress;
  final AnimationController animationController;

  const DetectionOverlayWidget({
    super.key,
    required this.detections,
    this.onDetectionTap,
    required this.onDetectionLongPress,
    required this.animationController,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: detections.map((detection) {
        return DetectionBoundingBox(
          detection: detection,
          onTap: onDetectionTap != null ? () => onDetectionTap!(detection) : null,
          onLongPress: () => onDetectionLongPress(detection),
          animationController: animationController,
        );
      }).toList(),
    );
  }
}

class DetectionBoundingBox extends StatelessWidget {
  final Detection detection;
  final VoidCallback? onTap;
  final VoidCallback onLongPress;
  final AnimationController animationController;

  const DetectionBoundingBox({
    super.key,
    required this.detection,
    this.onTap,
    required this.onLongPress,
    required this.animationController,
  });

  @override
  Widget build(BuildContext context) {
    final confidenceColor = _getConfidenceColor(detection.confidence);

    return Positioned(
      left: detection.x,
      top: detection.y,
      child: GestureDetector(
        onTap: onTap,
        onLongPress: onLongPress,
        child: AnimatedBuilder(
          animation: animationController,
          builder: (context, child) {
            final pulseValue = (animationController.value * 2 * 3.14159);
            final opacity = 0.7 + (0.3 * (1 + (pulseValue.sin() * 0.5)));

            return Container(
              width: detection.width,
              height: detection.height,
              decoration: BoxDecoration(
                border: Border.all(
                  color: confidenceColor.withValues(alpha: opacity),
                  width: 2.5,
                ),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Stack(
                children: [
                  // Corner indicators
                  Positioned(
                    top: -1,
                    left: -1,
                    child: Container(
                      width: 20,
                      height: 20,
                      decoration: BoxDecoration(
                        color: confidenceColor,
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(4),
                          bottomRight: Radius.circular(4),
                        ),
                      ),
                    ),
                  ),

                  // Label and confidence
                  Positioned(
                    top: -32,
                    left: 0,
                    child: Container(
                      constraints: BoxConstraints(
                        maxWidth: detection.width + 40,
                        minWidth: 60,
                      ),
                      padding: EdgeInsets.symmetric(
                          horizontal: 2.w, vertical: 0.5.h),
                      decoration: BoxDecoration(
                        color: confidenceColor,
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.3),
                            blurRadius: 4,
                            offset: Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            detection.label,
                            style: AppTheme.lightTheme.textTheme.labelMedium
                                ?.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.w600,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                          Text(
                            '${(detection.confidence * 100).toInt()}%',
                            style: AppTheme.lightTheme.textTheme.labelSmall
                                ?.copyWith(
                              color: Colors.white.withValues(alpha: 0.9),
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  // Confidence indicator
                  Positioned(
                    bottom: 4,
                    right: 4,
                    child: Container(
                      width: 8,
                      height: 8,
                      decoration: BoxDecoration(
                        color: confidenceColor,
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: confidenceColor.withValues(alpha: 0.5),
                            blurRadius: 4,
                            spreadRadius: 1,
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Color _getConfidenceColor(double confidence) {
    if (confidence >= 0.8) {
      return AppTheme.getSuccessColor(false); // High confidence - green
    } else if (confidence >= 0.6) {
      return AppTheme.getWarningColor(false); // Medium confidence - orange
    } else {
      return AppTheme.getErrorColor(false); // Low confidence - red
    }
  }
}
