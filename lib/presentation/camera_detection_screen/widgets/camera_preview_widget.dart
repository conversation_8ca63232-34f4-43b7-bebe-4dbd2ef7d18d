import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import 'package:camera/camera.dart';

import '../../../core/app_export.dart';

class CameraPreviewWidget extends StatefulWidget {
  final CameraService cameraService;
  final Function(double) onPinchToZoom;

  const CameraPreviewWidget({
    super.key,
    required this.cameraService,
    required this.onPinchToZoom,
  });

  @override
  State<CameraPreviewWidget> createState() => _CameraPreviewWidgetState();
}

class _CameraPreviewWidgetState extends State<CameraPreviewWidget> {
  double _baseScaleFactor = 1.0;
  double _scaleFactor = 1.0;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: Colors.black,
      child: GestureDetector(
        onScaleStart: (details) {
          _baseScaleFactor = widget.cameraService.zoomLevel;
        },
        onScaleUpdate: (details) {
          final newZoom = (_baseScaleFactor * details.scale).clamp(
            widget.cameraService.minZoom,
            widget.cameraService.maxZoom,
          );
          widget.onPinchToZoom(newZoom);
        },
        child: _buildCameraPreview(),
      ),
    );
  }

  Widget _buildCameraPreview() {
    if (!widget.cameraService.isInitialized) {
      return _buildLoadingPreview();
    }

    if (widget.cameraService.error != null) {
      return _buildErrorPreview();
    }

    return ClipRect(
      child: OverflowBox(
        alignment: Alignment.center,
        child: FittedBox(
          fit: BoxFit.fitWidth,
          child: SizedBox(
            width: widget.cameraService.controller!.value.previewSize!.height,
            height: widget.cameraService.controller!.value.previewSize!.width,
            child: CameraPreview(widget.cameraService.controller!),
          ),
        ),
      ),
    );
  }

  Widget _buildLoadingPreview() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.grey[800]!,
            Colors.grey[600]!,
            Colors.grey[700]!,
          ],
        ),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              color: AppTheme.primaryLight,
            ),
            SizedBox(height: 2.h),
            Text(
              'Initializing Camera...',
              style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                color: Colors.white.withValues(alpha: 0.7),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorPreview() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.red[900]!,
            Colors.red[700]!,
            Colors.red[800]!,
          ],
        ),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CustomIconWidget(
              iconName: 'error',
              color: Colors.white.withValues(alpha: 0.7),
              size: 80,
            ),
            SizedBox(height: 2.h),
            Text(
              'Camera Error',
              style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                color: Colors.white.withValues(alpha: 0.9),
              ),
            ),
            SizedBox(height: 1.h),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 4.w),
              child: Text(
                widget.cameraService.error ?? 'Unknown camera error',
                style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                  color: Colors.white.withValues(alpha: 0.7),
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ),
      ),
    );
  }
}


