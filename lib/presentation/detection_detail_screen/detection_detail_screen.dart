import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:sizer/sizer.dart';

import '../../core/app_export.dart';
import '../../widgets/custom_icon_widget.dart';
import './widgets/detection_metadata_widget.dart';
import './widgets/detection_objects_list_widget.dart';
import './widgets/zoomable_image_widget.dart';

class DetectionDetailScreen extends StatefulWidget {
  const DetectionDetailScreen({super.key});

  @override
  State<DetectionDetailScreen> createState() => _DetectionDetailScreenState();
}

class _DetectionDetailScreenState extends State<DetectionDetailScreen>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;

  int? _selectedObjectIndex;
  bool _isVoicePlaybackActive = false;
  bool _isEditMode = false;

  // Mock detection data
  final Map<String, dynamic> _detectionData = {
    "id": "detection_001",
    "imageUrl":
        "https://images.pexels.com/photos/1108099/pexels-photo-1108099.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1",
    "timestamp": DateTime.now().subtract(Duration(hours: 2)),
    "processingTime": 0.234,
    "cameraSettings": {
      "resolution": "1920x1080",
      "iso": 400,
      "exposure": "1/60s",
      "focusMode": "Auto"
    },
    "detectedObjects": [
      {
        "id": 0,
        "label": "Person",
        "confidence": 0.94,
        "boundingBox": {"x": 0.2, "y": 0.15, "width": 0.3, "height": 0.6},
        "isEditable": true
      },
      {
        "id": 1,
        "label": "Bicycle",
        "confidence": 0.87,
        "boundingBox": {"x": 0.5, "y": 0.4, "width": 0.35, "height": 0.4},
        "isEditable": true
      },
      {
        "id": 2,
        "label": "Car",
        "confidence": 0.76,
        "boundingBox": {"x": 0.1, "y": 0.6, "width": 0.4, "height": 0.25},
        "isEditable": true
      }
    ],
    "availableLabels": [
      "Person",
      "Bicycle",
      "Car",
      "Motorcycle",
      "Bus",
      "Truck",
      "Dog",
      "Cat",
      "Bird",
      "Horse",
      "Sheep",
      "Cow",
      "Elephant",
      "Bear",
      "Zebra",
      "Giraffe"
    ]
  };

  @override
  void initState() {
    super.initState();
    _fadeController = AnimationController(
      duration: Duration(milliseconds: 300),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );
    _fadeController.forward();
  }

  @override
  void dispose() {
    _fadeController.dispose();
    super.dispose();
  }

  void _onObjectTapped(int index) {
    setState(() {
      _selectedObjectIndex = _selectedObjectIndex == index ? null : index;
    });
    HapticFeedback.lightImpact();
  }

  void _shareDetection() {
    HapticFeedback.mediumImpact();
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40.w,
              height: 4,
              margin: EdgeInsets.symmetric(vertical: 12),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.outline,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            Padding(
              padding: EdgeInsets.all(20),
              child: Column(
                children: [
                  Text(
                    'Share Detection',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  SizedBox(height: 20),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      _buildShareOption(
                        icon: 'image',
                        label: 'Image',
                        onTap: () => _shareAsImage(),
                      ),
                      _buildShareOption(
                        icon: 'description',
                        label: 'JSON',
                        onTap: () => _shareAsJson(),
                      ),
                      _buildShareOption(
                        icon: 'table_chart',
                        label: 'CSV',
                        onTap: () => _shareAsCsv(),
                      ),
                    ],
                  ),
                  SizedBox(height: 20),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildShareOption({
    required String icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.primaryContainer,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          children: [
            CustomIconWidget(
              iconName: icon,
              size: 24,
              color: Theme.of(context).colorScheme.primary,
            ),
            SizedBox(height: 8),
            Text(
              label,
              style: Theme.of(context).textTheme.labelMedium,
            ),
          ],
        ),
      ),
    );
  }

  void _shareAsImage() {
    Navigator.pop(context);
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Sharing image with detection data...')),
    );
  }

  void _shareAsJson() {
    Navigator.pop(context);
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Exporting detection data as JSON...')),
    );
  }

  void _shareAsCsv() {
    Navigator.pop(context);
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Exporting detection data as CSV...')),
    );
  }

  void _toggleVoicePlayback() {
    setState(() {
      _isVoicePlaybackActive = !_isVoicePlaybackActive;
    });

    if (_isVoicePlaybackActive) {
      _playVoiceDescription();
    }
  }

  void _playVoiceDescription() {
    final objects = _detectionData['detectedObjects'] as List;
    final description = objects.map((obj) {
      final confidence = ((obj['confidence'] as double) * 100).round();
      return "${obj['label']} detected with $confidence% confidence";
    }).join('. ');

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Playing: $description'),
        duration: Duration(seconds: 3),
      ),
    );

    // Simulate voice playback completion
    Future.delayed(Duration(seconds: 3), () {
      if (mounted) {
        setState(() {
          _isVoicePlaybackActive = false;
        });
      }
    });
  }

  void _toggleEditMode() {
    setState(() {
      _isEditMode = !_isEditMode;
    });
    HapticFeedback.lightImpact();
  }

  void _deleteDetection() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Delete Detection'),
        content: Text(
          'Are you sure you want to delete this detection? This action cannot be undone and will permanently remove the detection data and associated image.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('Detection deleted successfully')),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
            child: Text('Delete'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      body: SafeArea(
        child: FadeTransition(
          opacity: _fadeAnimation,
          child: Column(
            children: [
              _buildAppBar(),
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      _buildImageSection(),
                      _buildMetadataSection(),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAppBar() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.h),
      child: Row(
        children: [
          GestureDetector(
            onTap: () => Navigator.pop(context),
            child: Container(
              padding: EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Theme.of(context).colorScheme.shadow,
                    blurRadius: 8,
                    offset: Offset(0, 2),
                  ),
                ],
              ),
              child: CustomIconWidget(
                iconName: 'arrow_back',
                size: 24,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
          ),
          Expanded(
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 4.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Detection Details',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  Text(
                    '${(_detectionData['detectedObjects'] as List).length} objects detected',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                ],
              ),
            ),
          ),
          Row(
            children: [
              GestureDetector(
                onTap: _toggleVoicePlayback,
                child: Container(
                  padding: EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: _isVoicePlaybackActive
                        ? Theme.of(context).colorScheme.primary
                        : Theme.of(context).colorScheme.surface,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Theme.of(context).colorScheme.shadow,
                        blurRadius: 8,
                        offset: Offset(0, 2),
                      ),
                    ],
                  ),
                  child: CustomIconWidget(
                    iconName: _isVoicePlaybackActive ? 'stop' : 'volume_up',
                    size: 20,
                    color: _isVoicePlaybackActive
                        ? Theme.of(context).colorScheme.onPrimary
                        : Theme.of(context).colorScheme.onSurface,
                  ),
                ),
              ),
              SizedBox(width: 2.w),
              GestureDetector(
                onTap: _shareDetection,
                child: Container(
                  padding: EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.surface,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Theme.of(context).colorScheme.shadow,
                        blurRadius: 8,
                        offset: Offset(0, 2),
                      ),
                    ],
                  ),
                  child: CustomIconWidget(
                    iconName: 'share',
                    size: 20,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildImageSection() {
    return Container(
      height: 50.h,
      margin: EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.h),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).colorScheme.shadow,
            blurRadius: 12,
            offset: Offset(0, 4),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: ZoomableImageWidget(
          imageUrl: _detectionData['imageUrl'] as String,
          detectedObjects: _detectionData['detectedObjects'] as List,
          selectedObjectIndex: _selectedObjectIndex,
          onObjectTapped: _onObjectTapped,
          isEditMode: _isEditMode,
          availableLabels: _detectionData['availableLabels'] as List<String>,
          onLabelChanged: (index, newLabel) {
            setState(() {
              (_detectionData['detectedObjects'] as List)[index]['label'] =
                  newLabel;
            });
          },
        ),
      ),
    );
  }

  Widget _buildMetadataSection() {
    return Container(
      margin: EdgeInsets.all(4.w),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _toggleEditMode,
                  icon: CustomIconWidget(
                    iconName: _isEditMode ? 'check' : 'edit',
                    size: 18,
                    color: Theme.of(context).colorScheme.onPrimary,
                  ),
                  label: Text(_isEditMode ? 'Save Changes' : 'Edit Labels'),
                ),
              ),
              SizedBox(width: 3.w),
              OutlinedButton.icon(
                onPressed: _deleteDetection,
                icon: CustomIconWidget(
                  iconName: 'delete',
                  size: 18,
                  color: Theme.of(context).colorScheme.error,
                ),
                label: Text(
                  'Delete',
                  style: TextStyle(color: Theme.of(context).colorScheme.error),
                ),
                style: OutlinedButton.styleFrom(
                  side: BorderSide(color: Theme.of(context).colorScheme.error),
                ),
              ),
            ],
          ),
          SizedBox(height: 3.h),
          DetectionMetadataWidget(
            timestamp: _detectionData['timestamp'] as DateTime,
            processingTime: _detectionData['processingTime'] as double,
            cameraSettings:
                _detectionData['cameraSettings'] as Map<String, dynamic>,
          ),
          SizedBox(height: 2.h),
          DetectionObjectsListWidget(
            detectedObjects: _detectionData['detectedObjects'] as List,
            selectedObjectIndex: _selectedObjectIndex,
            onObjectTapped: _onObjectTapped,
          ),
        ],
      ),
    );
  }
}
