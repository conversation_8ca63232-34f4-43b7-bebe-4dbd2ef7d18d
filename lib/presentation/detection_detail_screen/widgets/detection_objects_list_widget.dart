import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

class DetectionObjectsListWidget extends StatelessWidget {
  final List detectedObjects;
  final int? selectedObjectIndex;
  final Function(int) onObjectTapped;

  const DetectionObjectsListWidget({
    super.key,
    required this.detectedObjects,
    this.selectedObjectIndex,
    required this.onObjectTapped,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).colorScheme.shadow,
            blurRadius: 8,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                'Detected Objects',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
              ),
              Spacer(),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primaryContainer,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '${detectedObjects.length}',
                  style: Theme.of(context).textTheme.labelSmall?.copyWith(
                        color: Theme.of(context).colorScheme.primary,
                        fontWeight: FontWeight.w600,
                      ),
                ),
              ),
            ],
          ),
          SizedBox(height: 3.h),
          ListView.separated(
            shrinkWrap: true,
            physics: NeverScrollableScrollPhysics(),
            itemCount: detectedObjects.length,
            separatorBuilder: (context, index) => SizedBox(height: 2.h),
            itemBuilder: (context, index) {
              final obj = detectedObjects[index] as Map<String, dynamic>;
              final isSelected = selectedObjectIndex == index;

              return _buildObjectItem(
                context,
                index: index,
                obj: obj,
                isSelected: isSelected,
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildObjectItem(
    BuildContext context, {
    required int index,
    required Map<String, dynamic> obj,
    required bool isSelected,
  }) {
    final confidence = obj['confidence'] as double;
    final confidencePercentage = (confidence * 100).round();

    return GestureDetector(
      onTap: () {
        onObjectTapped(index);
        HapticFeedback.lightImpact();
      },
      child: AnimatedContainer(
        duration: Duration(milliseconds: 200),
        padding: EdgeInsets.all(4.w),
        decoration: BoxDecoration(
          color: isSelected
              ? Theme.of(context)
                  .colorScheme
                  .primaryContainer
                  .withValues(alpha: 0.3)
              : Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected
                ? Theme.of(context).colorScheme.primary
                : Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Row(
          children: [
            Container(
              width: 12.w,
              height: 12.w,
              decoration: BoxDecoration(
                color: _getConfidenceColor(context, confidence)
                    .withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Center(
                child: CustomIconWidget(
                  iconName: _getObjectIcon(obj['label'] as String),
                  size: 20,
                  color: _getConfidenceColor(context, confidence),
                ),
              ),
            ),
            SizedBox(width: 4.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          obj['label'] as String,
                          style: Theme.of(context)
                              .textTheme
                              .titleSmall
                              ?.copyWith(
                                fontWeight: FontWeight.w600,
                                color: isSelected
                                    ? Theme.of(context).colorScheme.primary
                                    : Theme.of(context).colorScheme.onSurface,
                              ),
                        ),
                      ),
                      Container(
                        padding:
                            EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                        decoration: BoxDecoration(
                          color: _getConfidenceColor(context, confidence),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          '$confidencePercentage%',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 10.sp,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 1.h),
                  Row(
                    children: [
                      Expanded(
                        child: _buildConfidenceBar(context, confidence),
                      ),
                      SizedBox(width: 3.w),
                      Text(
                        _getConfidenceLabel(confidence),
                        style: Theme.of(context).textTheme.labelSmall?.copyWith(
                              color: _getConfidenceColor(context, confidence),
                              fontWeight: FontWeight.w500,
                            ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            if (isSelected) ...[
              SizedBox(width: 3.w),
              CustomIconWidget(
                iconName: 'keyboard_arrow_right',
                size: 20,
                color: Theme.of(context).colorScheme.primary,
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildConfidenceBar(BuildContext context, double confidence) {
    return Container(
      height: 4,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(2),
      ),
      child: FractionallySizedBox(
        alignment: Alignment.centerLeft,
        widthFactor: confidence,
        child: Container(
          decoration: BoxDecoration(
            color: _getConfidenceColor(context, confidence),
            borderRadius: BorderRadius.circular(2),
          ),
        ),
      ),
    );
  }

  Color _getConfidenceColor(BuildContext context, double confidence) {
    final isLight = Theme.of(context).brightness == Brightness.light;

    if (confidence >= 0.8) {
      return AppTheme.getSuccessColor(isLight);
    } else if (confidence >= 0.6) {
      return AppTheme.getWarningColor(isLight);
    } else {
      return AppTheme.getErrorColor(isLight);
    }
  }

  String _getConfidenceLabel(double confidence) {
    if (confidence >= 0.8) {
      return 'High';
    } else if (confidence >= 0.6) {
      return 'Medium';
    } else {
      return 'Low';
    }
  }

  String _getObjectIcon(String label) {
    switch (label.toLowerCase()) {
      case 'person':
        return 'person';
      case 'bicycle':
        return 'directions_bike';
      case 'car':
        return 'directions_car';
      case 'motorcycle':
        return 'motorcycle';
      case 'bus':
        return 'directions_bus';
      case 'truck':
        return 'local_shipping';
      case 'dog':
      case 'cat':
        return 'pets';
      case 'bird':
        return 'flutter_dash';
      default:
        return 'category';
    }
  }
}
