import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';
import '../../../widgets/custom_icon_widget.dart';

class DetectionMetadataWidget extends StatelessWidget {
  final DateTime timestamp;
  final double processingTime;
  final Map<String, dynamic> cameraSettings;

  const DetectionMetadataWidget({
    super.key,
    required this.timestamp,
    required this.processingTime,
    required this.cameraSettings,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).colorScheme.shadow,
            blurRadius: 8,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Detection Metadata',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
          ),
          SizedBox(height: 3.h),
          _buildMetadataGrid(context),
        ],
      ),
    );
  }

  Widget _buildMetadataGrid(BuildContext context) {
    final metadataItems = [
      {
        'icon': 'schedule',
        'label': 'Timestamp',
        'value': _formatTimestamp(timestamp),
      },
      {
        'icon': 'speed',
        'label': 'Processing Time',
        'value': '${processingTime.toStringAsFixed(3)}s',
      },
      {
        'icon': 'photo_camera',
        'label': 'Resolution',
        'value': cameraSettings['resolution'] as String,
      },
      {
        'icon': 'iso',
        'label': 'ISO',
        'value': cameraSettings['iso'].toString(),
      },
      {
        'icon': 'shutter_speed',
        'label': 'Exposure',
        'value': cameraSettings['exposure'] as String,
      },
      {
        'icon': 'center_focus_strong',
        'label': 'Focus Mode',
        'value': cameraSettings['focusMode'] as String,
      },
    ];

    return GridView.builder(
      shrinkWrap: true,
      physics: NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 3.5,
        crossAxisSpacing: 3.w,
        mainAxisSpacing: 2.h,
      ),
      itemCount: metadataItems.length,
      itemBuilder: (context, index) {
        final item = metadataItems[index];
        return _buildMetadataItem(
          context,
          icon: item['icon'] as String,
          label: item['label'] as String,
          value: item['value'] as String,
        );
      },
    );
  }

  Widget _buildMetadataItem(
    BuildContext context, {
    required String icon,
    required String label,
    required String value,
  }) {
    return Container(
      padding: EdgeInsets.all(3.w),
      decoration: BoxDecoration(
        color: Theme.of(context)
            .colorScheme
            .primaryContainer
            .withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(8),
            decoration: BoxDecoration(
              color:
                  Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: CustomIconWidget(
              iconName: icon,
              size: 16,
              color: Theme.of(context).colorScheme.primary,
            ),
          ),
          SizedBox(width: 3.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  label,
                  style: Theme.of(context).textTheme.labelSmall?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                SizedBox(height: 2),
                Text(
                  value,
                  style: Theme.of(context).textTheme.labelMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }
}
