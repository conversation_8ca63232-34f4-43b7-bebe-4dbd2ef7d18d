import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

class ZoomableImageWidget extends StatefulWidget {
  final String imageUrl;
  final List detectedObjects;
  final int? selectedObjectIndex;
  final Function(int) onObjectTapped;
  final bool isEditMode;
  final List<String> availableLabels;
  final Function(int, String) onLabelChanged;

  const ZoomableImageWidget({
    super.key,
    required this.imageUrl,
    required this.detectedObjects,
    this.selectedObjectIndex,
    required this.onObjectTapped,
    required this.isEditMode,
    required this.availableLabels,
    required this.onLabelChanged,
  });

  @override
  State<ZoomableImageWidget> createState() => _ZoomableImageWidgetState();
}

class _ZoomableImageWidgetState extends State<ZoomableImageWidget>
    with TickerProviderStateMixin {
  late TransformationController _transformationController;
  late AnimationController _animationController;
  late Animation<Matrix4> _animationReset;

  @override
  void initState() {
    super.initState();
    _transformationController = TransformationController();
    _animationController = AnimationController(
      duration: Duration(milliseconds: 300),
      vsync: this,
    );
  }

  @override
  void dispose() {
    _transformationController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  void _resetZoom() {
    _animationReset = Matrix4Tween(
      begin: _transformationController.value,
      end: Matrix4.identity(),
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _animationController.reset();
    _animationController.forward();
    _animationReset.addListener(() {
      _transformationController.value = _animationReset.value;
    });
  }

  void _showLabelEditDialog(int objectIndex) {
    final currentLabel = widget.detectedObjects[objectIndex]['label'] as String;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Edit Object Label'),
        content: SizedBox(
          width: 80.w,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Select the correct label for this object:',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              SizedBox(height: 16),
              SizedBox(
                height: 40.h,
                child: ListView.builder(
                  itemCount: widget.availableLabels.length,
                  itemBuilder: (context, index) {
                    final label = widget.availableLabels[index];
                    final isSelected = label == currentLabel;

                    return ListTile(
                      title: Text(label),
                      leading: Radio<String>(
                        value: label,
                        groupValue: currentLabel,
                        onChanged: (value) {
                          if (value != null) {
                            widget.onLabelChanged(objectIndex, value);
                            Navigator.pop(context);
                          }
                        },
                      ),
                      selected: isSelected,
                      onTap: () {
                        widget.onLabelChanged(objectIndex, label);
                        Navigator.pop(context);
                      },
                    );
                  },
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Cancel'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        InteractiveViewer(
          transformationController: _transformationController,
          minScale: 0.5,
          maxScale: 4.0,
          child: Stack(
            children: [
              CustomImageWidget(
                imageUrl: widget.imageUrl,
                width: double.infinity,
                height: double.infinity,
                fit: BoxFit.cover,
              ),
              _buildBoundingBoxes(),
            ],
          ),
        ),
        Positioned(
          top: 16,
          right: 16,
          child: GestureDetector(
            onTap: _resetZoom,
            child: Container(
              padding: EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.6),
                borderRadius: BorderRadius.circular(8),
              ),
              child: CustomIconWidget(
                iconName: 'zoom_out_map',
                size: 20,
                color: Colors.white,
              ),
            ),
          ),
        ),
        if (widget.selectedObjectIndex != null) _buildSelectedObjectInfo(),
      ],
    );
  }

  Widget _buildBoundingBoxes() {
    return LayoutBuilder(
      builder: (context, constraints) {
        return Stack(
          children: widget.detectedObjects.asMap().entries.map((entry) {
            final index = entry.key;
            final obj = entry.value as Map<String, dynamic>;
            final bbox = obj['boundingBox'] as Map<String, dynamic>;
            final isSelected = widget.selectedObjectIndex == index;

            final left = (bbox['x'] as double) * constraints.maxWidth;
            final top = (bbox['y'] as double) * constraints.maxHeight;
            final width = (bbox['width'] as double) * constraints.maxWidth;
            final height = (bbox['height'] as double) * constraints.maxHeight;

            return Positioned(
              left: left,
              top: top,
              width: width,
              height: height,
              child: GestureDetector(
                onTap: () {
                  if (widget.isEditMode) {
                    _showLabelEditDialog(index);
                  } else {
                    widget.onObjectTapped(index);
                  }
                  HapticFeedback.lightImpact();
                },
                child: Container(
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: isSelected
                          ? Theme.of(context).colorScheme.primary
                          : _getConfidenceColor(obj['confidence'] as double),
                      width: isSelected ? 3 : 2,
                    ),
                    borderRadius: BorderRadius.circular(4),
                    color: isSelected
                        ? Theme.of(context)
                            .colorScheme
                            .primary
                            .withValues(alpha: 0.2)
                        : Colors.transparent,
                  ),
                  child: Stack(
                    children: [
                      Positioned(
                        top: -2,
                        left: -2,
                        child: Container(
                          padding:
                              EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                          decoration: BoxDecoration(
                            color: isSelected
                                ? Theme.of(context).colorScheme.primary
                                : _getConfidenceColor(
                                    obj['confidence'] as double),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            '${obj['label']} ${((obj['confidence'] as double) * 100).round()}%',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 10.sp,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ),
                      if (widget.isEditMode)
                        Positioned(
                          bottom: 4,
                          right: 4,
                          child: Container(
                            padding: EdgeInsets.all(2),
                            decoration: BoxDecoration(
                              color: Theme.of(context).colorScheme.primary,
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: CustomIconWidget(
                              iconName: 'edit',
                              size: 12,
                              color: Colors.white,
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            );
          }).toList(),
        );
      },
    );
  }

  Widget _buildSelectedObjectInfo() {
    final selectedObj = widget.detectedObjects[widget.selectedObjectIndex!]
        as Map<String, dynamic>;
    final confidence = ((selectedObj['confidence'] as double) * 100).round();

    return Positioned(
      bottom: 16,
      left: 16,
      right: 16,
      child: Container(
        padding: EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface.withValues(alpha: 0.95),
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Theme.of(context).colorScheme.shadow,
              blurRadius: 8,
              offset: Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    selectedObj['label'] as String,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                  ),
                ),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: _getConfidenceColor(
                        selectedObj['confidence'] as double),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '$confidence%',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 12.sp,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 8),
            Text(
              'Tap to deselect • ${widget.isEditMode ? 'Tap object to edit label' : 'Enable edit mode to modify labels'}',
              style: Theme.of(context).textTheme.bodySmall,
            ),
          ],
        ),
      ),
    );
  }

  Color _getConfidenceColor(double confidence) {
    if (confidence >= 0.8) {
      return AppTheme.getSuccessColor(
          Theme.of(context).brightness == Brightness.light);
    } else if (confidence >= 0.6) {
      return AppTheme.getWarningColor(
          Theme.of(context).brightness == Brightness.light);
    } else {
      return AppTheme.getErrorColor(
          Theme.of(context).brightness == Brightness.light);
    }
  }
}
